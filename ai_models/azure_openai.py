"""
Azure OpenAI Model Configuration

This module provides Azure OpenAI model setup and configuration for the CRM backend.
Supports both Azure OpenAI Service and standard OpenAI API endpoints with streaming capabilities.
"""

import os
import logging
from typing import Optional, Dict, Any, Union
from dataclasses import dataclass
from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI
from langchain.schema import BaseMessage

# Load environment variables from .env file
load_dotenv(dotenv_path='.env', override=True)  # pylint: disable=wrong-import-position

logger = logging.getLogger(__name__)


@dataclass
class AzureOpenAIConfiguration:
    """Configuration for Azure OpenAI Service."""
    api_key: str
    endpoint: str
    api_version: str = "2023-07-01-preview"
    deployment_name: Optional[str] = None

    @classmethod
    def from_env(cls) -> Optional['AzureOpenAIConfiguration']:
        """Create configuration from environment variables."""
        api_key = os.getenv("AZURE_OPENAI_API_KEY")
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview")
        deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")

        # Normalize endpoint format
        if endpoint and not endpoint.endswith('/'):
            endpoint = endpoint + '/'

        print(f"endpoint: {endpoint}")
        print(f"api_version: {api_version}")
        print(f"deployment_name: {deployment_name}")

        logger.info(f"Azure OpenAI Config - API Key: {'***' if api_key else 'None'}")
        logger.info(f"Azure OpenAI Config - Endpoint: {endpoint}")
        logger.info(f"Azure OpenAI Config - API Version: {api_version}")
        logger.info(f"Azure OpenAI Config - Deployment: {deployment_name}")

        if not api_key or not endpoint:
            logger.warning("Azure OpenAI configuration incomplete - missing API key or endpoint")
            return None

        if not deployment_name:
            logger.warning("Azure OpenAI deployment name is required but not provided")
            return None

        return cls(
            api_key=api_key,
            endpoint=endpoint,
            api_version=api_version,
            deployment_name=deployment_name
        )


class AzureOpenAIModel:
    """
    Azure OpenAI model wrapper with fallback to standard OpenAI.

    This class provides a unified interface for both Azure OpenAI Service
    and standard OpenAI API, with automatic configuration detection.
    """

    def __init__(self,
                 model_name: Optional[str] = None,
                 temperature: float = 0.3,
                 max_tokens: int = 1500,
                 streaming: bool = False,
                 **kwargs):
        """
        Initialize Azure OpenAI model with configuration.

        Args:
            model_name: Name of the model/deployment to use
            temperature: Temperature for response generation (0.0-2.0)
            max_tokens: Maximum tokens for responses
            streaming: Enable streaming responses
            **kwargs: Additional model parameters
        """
        self.model_name = model_name or self._get_default_model()
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.streaming = streaming
        self.kwargs = kwargs
        self.llm = None
        self.azure_config = None
        self._setup_model()

    def _get_default_model(self) -> str:
        """Get default model name from environment."""
        return (
            os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME") or
            os.getenv("AZURE_OPENAI_MODEL") or
            os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        )
    
    def _setup_model(self):
        """Setup the Azure OpenAI model (Azure-only, no fallback)."""
        try:
            # Load Azure OpenAI configuration
            self.azure_config = AzureOpenAIConfiguration.from_env()
            if self.azure_config:
                self.llm = self._create_azure_model()
                logger.info(f"✅ Initialized Azure OpenAI model: {self.model_name} (deployment: {self.azure_config.deployment_name})")
            else:
                # No fallback - Azure OpenAI is required
                logger.error("❌ Azure OpenAI configuration not found. Please check your environment variables.")
                logger.error("Required variables: AZURE_OPENAI_API_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME")
                self.llm = None

        except Exception as e:
            logger.error(f"❌ Failed to initialize Azure OpenAI model: {e}")
            self.llm = None
            self.azure_config = None

    def _has_azure_config(self) -> bool:
        """Check if Azure OpenAI configuration is available."""
        return self.azure_config is not None
    
    def _create_azure_model(self) -> AzureChatOpenAI:
        """Create Azure OpenAI model instance."""
        if not self.azure_config:
            raise ValueError("Azure configuration not available")

        # Use deployment name if available, otherwise use model name
        deployment_name = self.azure_config.deployment_name or self.model_name

        logger.info(f"Creating Azure OpenAI model with:")
        logger.info(f"  - Deployment: {deployment_name}")
        logger.info(f"  - Endpoint: {self.azure_config.endpoint}")
        logger.info(f"  - API Version: {self.azure_config.api_version}")

        try:
            return AzureChatOpenAI(
                azure_deployment=deployment_name,
                azure_endpoint=self.azure_config.endpoint,
                api_key=self.azure_config.api_key,
                api_version=self.azure_config.api_version,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                streaming=self.streaming,
                **self.kwargs
            )
        except Exception as e:
            logger.error(f"❌ Failed to create Azure OpenAI client: {e}")
            logger.error(f"   Check if deployment '{deployment_name}' exists in your Azure OpenAI resource")
            logger.error(f"   Endpoint: {self.azure_config.endpoint}")
            logger.error(f"   API Version: {self.azure_config.api_version}")
            raise


    
    async def ainvoke(self, messages: list[BaseMessage]) -> Any:
        """
        Async invoke the model with messages.
        
        Args:
            messages: List of messages to send to the model
            
        Returns:
            Model response
            
        Raises:
            RuntimeError: If model is not available
        """
        if not self.llm:
            raise RuntimeError("AI model is not available. Please check configuration.")
        
        return await self.llm.ainvoke(messages)
    
    def invoke(self, messages: list[BaseMessage]) -> Any:
        """
        Sync invoke the model with messages.
        
        Args:
            messages: List of messages to send to the model
            
        Returns:
            Model response
            
        Raises:
            RuntimeError: If model is not available
        """
        if not self.llm:
            raise RuntimeError("AI model is not available. Please check configuration.")
        
        return self.llm.invoke(messages)
    
    def is_available(self) -> bool:
        """Check if the model is available for use."""
        return self.llm is not None
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive information about the current Azure OpenAI model configuration."""
        base_info = {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "streaming": self.streaming,
            "is_azure": True,  # Always Azure
            "is_available": self.is_available(),
            "provider": "Azure OpenAI"
        }

        # Add Azure-specific information if available
        if self.azure_config:
            base_info.update({
                "azure_endpoint": self.azure_config.endpoint,
                "azure_api_version": self.azure_config.api_version,
                "azure_deployment": self.azure_config.deployment_name or self.model_name,
            })
        else:
            base_info.update({
                "error": "Azure OpenAI configuration not found"
            })

        return base_info

    def create_chat_completion(self, messages: list[BaseMessage], **kwargs) -> Any:
        """
        Create a chat completion (non-streaming).

        Args:
            messages: List of messages to send to the model
            **kwargs: Additional parameters for the completion

        Returns:
            Model response
        """
        if not self.llm:
            raise RuntimeError("AI model is not available. Please check configuration.")

        # Merge kwargs with instance settings
        completion_kwargs = {**self.kwargs, **kwargs}

        return self.llm.invoke(messages, **completion_kwargs)

    async def create_chat_completion_async(self, messages: list[BaseMessage], **kwargs) -> Any:
        """
        Create a chat completion asynchronously (non-streaming).

        Args:
            messages: List of messages to send to the model
            **kwargs: Additional parameters for the completion

        Returns:
            Model response
        """
        if not self.llm:
            raise RuntimeError("AI model is not available. Please check configuration.")

        # Merge kwargs with instance settings
        completion_kwargs = {**self.kwargs, **kwargs}

        return await self.llm.ainvoke(messages, **completion_kwargs)


# Global instance for easy importing (lazy-loaded)
_azure_openai_model = None

def get_global_azure_openai_model() -> AzureOpenAIModel:
    """Get the global Azure OpenAI model instance (lazy-loaded)."""
    global _azure_openai_model
    if _azure_openai_model is None:
        _azure_openai_model = AzureOpenAIModel()
    return _azure_openai_model


def get_azure_openai_model(
    model_name: Optional[str] = None,
    temperature: float = 0.3,
    max_tokens: int = 1500,
    streaming: bool = False,
    **kwargs
) -> AzureOpenAIModel:
    """
    Get an Azure OpenAI model instance with custom configuration.

    This function creates a new instance of AzureOpenAIModel configured
    specifically for Azure OpenAI Service (no fallback to standard OpenAI).

    Required Environment Variables:
        - AZURE_OPENAI_API_KEY: Your Azure OpenAI API key
        - AZURE_OPENAI_ENDPOINT: Your Azure OpenAI endpoint URL (e.g., https://your-resource.openai.azure.com/)
        - AZURE_OPENAI_DEPLOYMENT_NAME: Your deployment name (required)

    Optional Environment Variables:
        - AZURE_OPENAI_API_VERSION: API version (default: 2024-02-15-preview)
        - AZURE_OPENAI_MODEL: Model name (fallback if deployment name not set)

    Args:
        model_name: Name of the model/deployment to use
        temperature: Temperature for response generation (0.0-2.0)
        max_tokens: Maximum tokens for responses
        streaming: Enable streaming responses
        **kwargs: Additional model parameters

    Returns:
        AzureOpenAIModel instance configured for Azure OpenAI Service

    Example:
        >>> # Basic usage
        >>> model = get_azure_openai_model()
        >>>
        >>> # Custom configuration
        >>> model = get_azure_openai_model(
        ...     model_name="gpt-4",
        ...     temperature=0.7,
        ...     max_tokens=2000,
        ...     streaming=True
        ... )
    """
    return AzureOpenAIModel(
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
        streaming=streaming,
        **kwargs
    )
