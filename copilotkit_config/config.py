"""
CopilotKit Configuration

Centralized configuration for CopilotKit runtime, agents, and actions.
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class CopilotKitConfig(BaseModel):
    """CopilotKit configuration settings."""
    
    # Runtime Configuration
    endpoint_path: str = Field(default="/copilotkit", description="CopilotKit endpoint path")
    cors_origins: List[str] = Field(default=["*"], description="CORS allowed origins")
    cors_methods: List[str] = Field(default=["GET", "POST", "OPTIONS"], description="CORS allowed methods")
    cors_headers: List[str] = Field(
        default=[
            "Content-Type", 
            "Authorization", 
            "x-copilotkit-runtime-client-gql-version",
            "x-requested-with"
        ],
        description="CORS allowed headers"
    )
    
    # Agent Configuration
    default_agent_model: str = Field(default="gpt-4o-mini", description="Default LLM model for agents")
    agent_temperature: float = Field(default=0.3, description="Default temperature for agent responses")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens for agent responses")
    
    # Authentication
    require_auth: bool = Field(default=False, description="Whether authentication is required")
    jwt_secret: Optional[str] = Field(default=None, description="JWT secret for authentication")
    
    # Database
    database_url: Optional[str] = Field(default=None, description="Database connection URL (required for production)")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_actions: bool = Field(default=True, description="Whether to log action executions")
    
    # Rate Limiting
    rate_limit_enabled: bool = Field(default=False, description="Whether rate limiting is enabled")
    rate_limit_requests: int = Field(default=100, description="Requests per minute limit")
    
    # Note: For environment variable support, consider using pydantic-settings
    # class Config:
    #     env_file = ".env"
    #     env_prefix = "COPILOTKIT_"


# Global configuration instance
config = CopilotKitConfig()


def get_config() -> CopilotKitConfig:
    """Get the global CopilotKit configuration."""
    return config


def update_config(**kwargs) -> None:
    """Update configuration values."""
    global config
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
