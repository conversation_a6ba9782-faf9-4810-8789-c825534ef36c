"""
CopilotKit Runtime Configuration

This module provides utilities for creating and configuring the CopilotKit runtime
with proper error handling and logging.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import FastAPI
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent, Action

from .config import get_config


logger = logging.getLogger(__name__)


class CopilotKitRuntime:
    """Enhanced CopilotKit runtime with proper configuration."""
    
    def __init__(
        self,
        agents: Optional[List[LangGraphAgent]] = None,
        actions: Optional[List[Action]] = None
    ):
        self.config = get_config()
        self.agents = agents or []
        self.actions = actions or []
        self.sdk = None
        
    def create_sdk(self) -> CopilotKitRemoteEndpoint:
        """Create the CopilotKit SDK with configured agents and actions."""
        if self.sdk is None:
            self.sdk = CopilotKitRemoteEndpoint(
                agents=self.agents,
                actions=self.actions
            )
            logger.info(f"Created CopilotKit SDK with {len(self.agents)} agents and {len(self.actions)} actions")
        return self.sdk
    
    def add_agent(self, agent: LangGraphAgent) -> None:
        """Add an agent to the runtime."""
        self.agents.append(agent)
        logger.info(f"Added agent: {agent.name}")
        # Reset SDK to recreate with new agent
        self.sdk = None
    
    def add_action(self, action: Action) -> None:
        """Add an action to the runtime."""
        self.actions.append(action)
        logger.info(f"Added action: {action.name}")
        # Reset SDK to recreate with new action
        self.sdk = None
    
    def add_actions(self, actions: List[Action]) -> None:
        """Add multiple actions to the runtime."""
        self.actions.extend(actions)
        logger.info(f"Added {len(actions)} actions")
        # Reset SDK to recreate with new actions
        self.sdk = None
    
    def setup_fastapi_endpoint(self, app: FastAPI) -> None:
        """Setup the CopilotKit endpoint on a FastAPI app."""
        sdk = self.create_sdk()

        # Add the CopilotKit endpoint
        add_fastapi_endpoint(app, sdk, self.config.endpoint_path)

        logger.info(f"CopilotKit endpoint added at {self.config.endpoint_path}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get runtime statistics."""
        return {
            "agents_count": len(self.agents),
            "actions_count": len(self.actions),
            "endpoint_path": self.config.endpoint_path,
            "auth_required": self.config.require_auth
        }


def create_copilotkit_runtime(
    agents: Optional[List[LangGraphAgent]] = None,
    actions: Optional[List[Action]] = None,
    **kwargs
) -> CopilotKitRuntime:
    """
    Factory function to create a CopilotKit runtime with best practices.
    
    Args:
        agents: List of LangGraph agents
        actions: List of CopilotKit actions
        **kwargs: Additional configuration options
        
    Returns:
        Configured CopilotKit runtime
    """
    # Update configuration with any provided kwargs
    config = get_config()
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    runtime = CopilotKitRuntime(agents=agents, actions=actions)
    
    logger.info("CopilotKit runtime created successfully")
    return runtime
