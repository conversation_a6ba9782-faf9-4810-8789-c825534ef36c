"""
Core Business Logic Module

This module contains core business logic components including database operations,
authentication, validation, and services.
"""

# Re-export commonly used components for backward compatibility
from agents.lead_agent.database import db_manager
from agents.lead_agent.validation import validator
from agents.lead_agent.context import get_auth_context

__all__ = [
    "db_manager",
    "validator", 
    "get_auth_context"
]
