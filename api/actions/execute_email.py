"""
Execute Email Action API Router

This module provides endpoints for executing email draft actions generated by the AI agents.
"""

import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
# datetime import removed - no longer constructing manual IDs
from agents.lead_agent.database import db_manager

logger = logging.getLogger(__name__)

router = APIRouter()


class ExecuteEmailRequest(BaseModel):
    """Request model for executing email actions."""
    emailData: Dict[str, Any]
    leadId: Optional[str] = None  # Lead ID to fetch tenant and owner details
    tenantId: Optional[str] = None  # Fallback if leadId not provided


class ExecuteEmailResponse(BaseModel):
    """Response model for email execution."""
    success: bool
    emailId: Optional[str] = None
    status: str
    message: str
    error: Optional[str] = None


@router.post("/execute-email", response_model=ExecuteEmailResponse, summary="Execute Email Draft Action")
async def execute_email_action(request: ExecuteEmailRequest):
    """
    Execute an email draft action by creating an EmailDraft record in the database.

    This endpoint takes the email data generated by the AI agent and creates
    a draft email record that can be reviewed and sent later.

    Args:
        request: ExecuteEmailRequest containing email data and lead/tenant info

    Returns:
        ExecuteEmailResponse with execution status and email ID
    """
    try:
        logger.info("Executing email draft action")

        # Validate email data
        email_data = request.emailData
        if not email_data:
            raise HTTPException(status_code=400, detail="Email data is required")

        # Validate required email fields
        required_fields = ["subject", "to"]
        for field in required_fields:
            if not email_data.get(field):
                raise HTTPException(status_code=400, detail=f"Email {field} is required")

        # Get lead details if leadId is provided
        lead_data = None
        tenant_id = request.tenantId
        owner_id = None

        if request.leadId:
            try:
                # Fetch lead details from database
                lead_data = await db_manager.get_lead_by_id(request.leadId, tenant_id or "")
                if lead_data:
                    tenant_id = lead_data.get("tenantId")
                    owner_id = lead_data.get("userId")  # Lead owner
                    logger.info(f"Retrieved lead data for ID: {request.leadId}")
                else:
                    logger.warning(f"Lead not found for ID: {request.leadId}")
            except Exception as e:
                logger.error(f"Error fetching lead data: {e}")
                # Continue without lead data if fetch fails

        # Enhance email data with lead information
        enhanced_email_data = {
            **email_data,
            "tenantId": tenant_id,
            "ownerId": owner_id,
            "leadId": request.leadId,
            "reference_modal": "Lead" if request.leadId else email_data.get("reference_modal", "Note"),
            "reference_modalId": request.leadId or email_data.get("reference_modalId"),
        }

        # Create actual email draft in database using Prisma
        try:
            # Prepare email data for database insertion
            email_create_data = {
                "from": enhanced_email_data.get("from"),
                "to": enhanced_email_data.get("to", []),
                "subject": enhanced_email_data.get("subject"),
                "htmlContent": enhanced_email_data.get("content"),
                "status": "draft",
                "reference_modal": enhanced_email_data["reference_modal"],
                "reference_modalId": enhanced_email_data["reference_modalId"],
                "deleted": False
            }

            # Add tenant if available
            if tenant_id:
                email_create_data["tenantId"] = tenant_id

            # Create email draft in database - try different model names
            created_email = None
            email_model_names = ['emaildraft', 'email_draft', 'EmailDraft']

            for model_name in email_model_names:
                try:
                    if hasattr(db_manager._client, model_name):
                        model = getattr(db_manager._client, model_name)
                        created_email = await model.create(data=email_create_data)
                        logger.info(f"Successfully created email using model: {model_name}")
                        break
                except Exception as model_error:
                    logger.warning(f"Failed to create email with model {model_name}: {model_error}")
                    continue

            if not created_email:
                raise Exception("Could not create email with any available model")

            email_id = created_email.id
            logger.info(f"Successfully created email draft in database with ID: {email_id} for tenant: {tenant_id}")

        except Exception as db_error:
            logger.error(f"Database error creating email draft: {db_error}")
            # Don't create fake IDs - return error instead
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create email draft in database: {str(db_error)}"
            )

        return ExecuteEmailResponse(
            success=True,
            emailId=email_id,
            status="draft",
            message="Email draft created successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error executing email action: {e}")
        return ExecuteEmailResponse(
            success=False,
            status="failed",
            message="Failed to create email draft",
            error=str(e)
        )


@router.post("/send-email/{email_id}", summary="Send Email Draft")
async def send_email_draft(email_id: str):
    """
    Send a previously created email draft.
    
    Args:
        email_id: ID of the email draft to send
        
    Returns:
        ExecuteEmailResponse with send status
    """
    try:
        logger.info(f"Sending email draft: {email_id}")
        
        # TODO: Implement email sending logic
        # This would typically:
        # 1. Retrieve the email draft from database
        # 2. Send the email using an email service
        # 3. Update the email status to "sent"
        
        return ExecuteEmailResponse(
            success=True,
            emailId=email_id,
            status="sent",
            message="Email sent successfully"
        )
        
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return ExecuteEmailResponse(
            success=False,
            emailId=email_id,
            status="failed",
            message="Failed to send email",
            error=str(e)
        )
