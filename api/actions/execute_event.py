"""
Execute Event Action API Router

This module provides endpoints for executing event creation actions generated by the AI agents.
"""

import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from datetime import datetime
from agents.lead_agent.database import db_manager

logger = logging.getLogger(__name__)

router = APIRouter()


class ExecuteEventRequest(BaseModel):
    """Request model for executing event actions."""
    eventData: Dict[str, Any]
    leadId: Optional[str] = None  # Lead ID to fetch tenant and owner details
    tenantId: Optional[str] = None  # Fallback if leadId not provided


class ExecuteEventResponse(BaseModel):
    """Response model for event execution."""
    success: bool
    eventId: Optional[str] = None
    status: str
    message: str
    error: Optional[str] = None


@router.post("/execute-event", response_model=ExecuteEventResponse, summary="Execute Event Creation Action")
async def execute_event_action(request: ExecuteEventRequest):
    """
    Execute an event creation action by creating an EventItem record in the database.

    This endpoint takes the event data generated by the AI agent and creates
    a calendar event record that can be scheduled and managed.

    Args:
        request: ExecuteEventRequest containing event data and lead/tenant info

    Returns:
        ExecuteEventResponse with execution status and event ID
    """
    try:
        logger.info("Executing event creation action")

        # Validate event data
        event_data = request.eventData
        if not event_data:
            raise HTTPException(status_code=400, detail="Event data is required")

        # Validate required event fields
        required_fields = ["title", "startTime", "endTime"]
        for field in required_fields:
            if not event_data.get(field):
                raise HTTPException(status_code=400, detail=f"Event {field} is required")

        # Get lead details if leadId is provided
        lead_data = None
        tenant_id = request.tenantId
        owner_id = None

        if request.leadId:
            try:
                # Fetch lead details from database
                lead_data = await db_manager.get_lead_by_id(request.leadId, tenant_id or "")
                if lead_data:
                    tenant_id = lead_data.get("tenantId")
                    owner_id = lead_data.get("userId")  # Lead owner
                    logger.info(f"Retrieved lead data for ID: {request.leadId}")
                else:
                    logger.warning(f"Lead not found for ID: {request.leadId}")
            except Exception as e:
                logger.error(f"Error fetching lead data: {e}")
                # Continue without lead data if fetch fails

        # Enhance event data with lead information
        enhanced_event_data = {
            **event_data,
            "tenantId": tenant_id,
            "ownerId": owner_id,
            "leadId": request.leadId,
            "reference_modal": "Lead" if request.leadId else event_data.get("reference_modal", "Note"),
            "reference_modalId": request.leadId or event_data.get("reference_modalId"),
        }
        
        # Create actual event in database using Prisma
        try:
            # Prepare event data for database insertion
            event_create_data = {
                "title": enhanced_event_data["title"],
                "description": enhanced_event_data.get("description"),
                "location": enhanced_event_data.get("location"),
                "attendees": enhanced_event_data.get("attendees", []),
                "eventType": enhanced_event_data.get("eventType", "meeting"),
                "status": enhanced_event_data.get("status", "scheduled"),
                "reference_modal": enhanced_event_data["reference_modal"],
                "reference_modalId": enhanced_event_data["reference_modalId"],
                "deleted": False
            }

            # Parse and add start/end times
            try:
                start_time = datetime.fromisoformat(enhanced_event_data["startTime"].replace('Z', '+00:00'))
                end_time = datetime.fromisoformat(enhanced_event_data["endTime"].replace('Z', '+00:00'))
                event_create_data["startTime"] = start_time
                event_create_data["endTime"] = end_time
            except (ValueError, AttributeError, KeyError) as date_error:
                logger.error(f"Invalid date format in event data: {date_error}")
                raise HTTPException(status_code=400, detail="Invalid startTime or endTime format")

            # Add tenant if available
            if tenant_id:
                event_create_data["tenantId"] = tenant_id

            # Create event in database - try different model names
            created_event = None
            event_model_names = ['eventitem', 'event_item', 'EventItem']

            for model_name in event_model_names:
                try:
                    if hasattr(db_manager._client, model_name):
                        model = getattr(db_manager._client, model_name)
                        created_event = await model.create(data=event_create_data)
                        logger.info(f"Successfully created event using model: {model_name}")
                        break
                except Exception as model_error:
                    logger.warning(f"Failed to create event with model {model_name}: {model_error}")
                    continue

            if not created_event:
                raise Exception("Could not create event with any available model")

            event_id = created_event.id
            logger.info(f"Successfully created event in database with ID: {event_id} for tenant: {tenant_id}")

        except HTTPException:
            # Re-raise HTTP exceptions (like date validation errors)
            raise
        except Exception as db_error:
            logger.error(f"Database error creating event: {db_error}")
            # Don't create fake IDs - return error instead
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create event in database: {str(db_error)}"
            )
        
        return ExecuteEventResponse(
            success=True,
            eventId=event_id,
            status="scheduled",
            message="Event created successfully"
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error executing event action: {e}")
        return ExecuteEventResponse(
            success=False,
            status="failed",
            message="Failed to create event",
            error=str(e)
        )


@router.patch("/update-event/{event_id}", summary="Update Event Status")
async def update_event_status(event_id: str, status: str):
    """
    Update the status of an event.
    
    Args:
        event_id: ID of the event to update
        status: New status (scheduled, completed, cancelled)
        
    Returns:
        ExecuteEventResponse with update status
    """
    try:
        logger.info(f"Updating event {event_id} status to: {status}")
        
        # Validate status
        valid_statuses = ["scheduled", "completed", "cancelled"]
        if status not in valid_statuses:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
            )
        
        # TODO: Implement database update using Prisma
        # This would typically:
        # 1. Find the event by ID
        # 2. Update the status field
        # 3. Return the updated record
        
        return ExecuteEventResponse(
            success=True,
            eventId=event_id,
            status=status,
            message=f"Event status updated to {status}"
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating event status: {e}")
        return ExecuteEventResponse(
            success=False,
            eventId=event_id,
            status="failed",
            message="Failed to update event status",
            error=str(e)
        )
