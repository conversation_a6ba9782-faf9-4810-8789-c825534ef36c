[tool.poetry]
name = "lead_agent"
version = "0.1.0"
description = "Lead Management AI Agent"
authors = ["Developer <<EMAIL>>"]
license = "MIT"
packages = [
    {include = "actions"},
    {include = "agents"},
    {include = "copilotkit_config"},
    {include = "core"},
    {include = "prompts"},
    {include = "webintel"}
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
langchain-openai = "^0.3.7"
langchain = "^0.3.1"
openai = "^1.51.0"
copilotkit = "0.1.45"
uvicorn = "^0.31.0"
python-dotenv = "^1.0.1"
langchain-core = "^0.3.25"
langgraph-cli = {extras = ["inmem"], version = "^0.1.64"}
prisma = "^0.15.0"
fastapi = "^0.115.0"
pyjwt = "^2.9.0"
beautifulsoup4 = "^4.13.4"
spacy = "^3.8.7"
pyyaml = "^6.0.1"

[tool.poetry.scripts]
demo = "main:main"
