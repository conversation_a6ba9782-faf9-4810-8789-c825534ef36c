"""
Simple Context Management for Lead Agent

This module provides a lightweight context management system for passing
authentication and request context to lead management actions.
"""

from typing import Optional, <PERSON><PERSON>
from threading import local

# Thread-local storage for context
_context = local()

class ContextError(Exception):
    """Exception raised when context is not available."""
    pass

def set_context(tenant_id: str, user_id: str) -> None:
    """
    Set the current context for the thread.
    
    Args:
        tenant_id: The tenant ID
        user_id: The user ID
    """
    _context.tenant_id = tenant_id
    _context.user_id = user_id

def get_context() -> Tuple[str, str]:
    """
    Get the current context.

    Returns:
        Tuple of (tenant_id, user_id)

    Raises:
        ContextError: If context is not set
    """
    tenant_id = getattr(_context, 'tenant_id', None)
    user_id = getattr(_context, 'user_id', None)

    # Provide default values when authentication is not required
    if not tenant_id:
        tenant_id = "default-tenant"
    if not user_id:
        user_id = "default-user"

    return tenant_id, user_id

def clear_context() -> None:
    """Clear the current context."""
    _context.tenant_id = None
    _context.user_id = None

def has_context() -> bool:
    """
    Check if context is available.
    
    Returns:
        True if context is set, False otherwise
    """
    tenant_id = getattr(_context, 'tenant_id', None)
    user_id = getattr(_context, 'user_id', None)
    return bool(tenant_id and user_id)

# Async wrapper for compatibility
async def get_auth_context() -> Tuple[str, str]:
    """
    Async wrapper for get_context().
    
    Returns:
        Tuple of (tenant_id, user_id)
        
    Raises:
        ContextError: If context is not set
    """
    return get_context()
