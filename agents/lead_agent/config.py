"""
Lead Agent Configuration

Configuration settings and constants for the lead management agent.
"""

from typing import Dict, Any, List
from pydantic import BaseModel, Field


class LeadAgentConfig(BaseModel):
    """Configuration for the Lead Agent."""
    
    # Model Configuration
    model_name: str = Field(default="gpt-4o-mini", description="LLM model to use")
    temperature: float = Field(default=0.3, description="Temperature for responses")
    max_tokens: int = Field(default=2000, description="Maximum tokens for responses")
    
    # Agent Behavior
    max_iterations: int = Field(default=10, description="Maximum agent iterations")
    enable_memory: bool = Field(default=True, description="Enable conversation memory")
    enable_tools: bool = Field(default=True, description="Enable tool usage")
    
    # Error Handling
    max_retries: int = Field(default=3, description="Maximum retries for failed operations")
    timeout_seconds: int = Field(default=30, description="Timeout for operations")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_tool_calls: bool = Field(default=True, description="Log tool calls")
    log_state_changes: bool = Field(default=False, description="Log state changes")


# Default configuration
DEFAULT_CONFIG = LeadAgentConfig()

# System prompts
SYSTEM_PROMPTS = {
    "main": """You are a specialized Lead Management AI Assistant. Your primary role is to help users manage leads in their CRM system efficiently and accurately.

**Your Capabilities:**
You have access to a single, powerful tool called 'update_details' that can handle ALL lead updates:
- Basic information: firstName, lastName, email, phone, company
- Business information: title, industry, source, website
- Custom fields: any organization-specific fields
- Multiple fields at once: you can update any combination of fields in a single operation

**Guidelines:**
- Use the unified 'update_details' tool for ALL lead updates
- Always ask for the lead ID if not provided
- You can update one field or multiple fields in a single call
- Validate email formats and website URLs before updating
- Provide clear feedback on what was updated
- Handle errors gracefully and suggest corrections
- Be professional, helpful, and focused on lead management tasks
- When making updates, confirm the changes with the user
- If uncertain about data, ask for clarification

**Data Validation:**
- Email addresses must be in valid format
- Phone numbers will be automatically cleaned
- Website URLs must include http:// or https://
- Custom fields must match their defined types

**Examples:**
- To update job title: use update_details(title="Software Developer")
- To update multiple fields: use update_details(title="Manager", email="<EMAIL>", company="New Corp")

Be concise but thorough in your responses.""",

    "error_recovery": """An error occurred while processing the lead management request. Please:
1. Review the error details provided
2. Suggest corrections if the error is due to invalid data
3. Offer alternative approaches if the operation failed
4. Ask for clarification if the request was unclear

Always maintain a helpful and professional tone when handling errors.""",

    "validation": """Before proceeding with lead updates, I need to validate the provided information:
- Lead ID must be provided and valid
- Email addresses must be in correct format
- Phone numbers will be cleaned automatically
- Website URLs must include protocol (http:// or https://)
- Custom field values must match their defined types

Please provide any missing or corrected information."""
}

# Tool categories and priorities
TOOL_CATEGORIES = {
    "lead_updates": {
        "priority": 1,
        "description": "All lead information updates (basic, business, custom fields)",
        "tools": ["update_details"]
    },
    "utilities": {
        "priority": 2,
        "description": "Utility functions",
        "tools": ["get_current_time"]
    }
}

# Error messages
ERROR_MESSAGES = {
    "lead_not_found": "Lead not found or you don't have access to it. Please check the lead ID and try again.",
    "validation_failed": "Data validation failed. Please check the provided information and correct any errors.",
    "auth_failed": "Authentication failed. Please ensure you're properly logged in.",
    "database_error": "Database operation failed. Please try again later.",
    "timeout": "Operation timed out. Please try again with a simpler request.",
    "unknown": "An unexpected error occurred. Please try again or contact support."
}

# Success messages
SUCCESS_MESSAGES = {
    "lead_updated": "Lead information updated successfully.",
    "bulk_update": "Bulk update completed successfully.",
    "validation_passed": "All data validation checks passed.",
    "operation_complete": "Operation completed successfully."
}
