"""
Lead Agent Graph Definition

Enhanced LangGraph workflow with improved error handling, state management,
and conditional routing based on agent status.
"""

import logging
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import LeadAgentState, AgentStatus, create_initial_state
from .config import DEFAULT_CONFIG
from .workflow_node import (
    agent_node,
    tool_execution_node,
    error_recovery_node,
    validation_node
)

logger = logging.getLogger(__name__)


def should_continue(state: LeadAgentState):
    """
    Enhanced routing function that determines the next step based on agent status and state.
    """
    status = state.get("status", AgentStatus.IDLE)
    retry_count = state.get("retry_count", 0)

    print(f"🔀 [GRAPH-ROUTER] Routing decision - Status: {status}, Retry: {retry_count}")

    # Handle error states
    if status == AgentStatus.ERROR:
        if retry_count < DEFAULT_CONFIG.max_retries:
            print(f"🔀 [GRAPH-ROUTER] Routing to error recovery (attempt {retry_count + 1})")
            logger.info(f"Routing to error recovery (attempt {retry_count + 1})")
            return "error_recovery"
        else:
            print(f"🔀 [GRAPH-ROUTER] Max retries exceeded, ending conversation")
            logger.warning("Max retries exceeded, ending conversation")
            return END
    
    # Handle validation requirements
    if status == AgentStatus.WAITING_FOR_INPUT:
        logger.info("Routing to validation")
        return "validation"
    
    # Handle tool execution
    if status == AgentStatus.EXECUTING_TOOL:
        # Check if we have tool calls in the last message
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1]
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                print(f"🔀 [GRAPH-ROUTER] Routing to tool execution - {len(last_message.tool_calls)} tools")
                logger.info("Routing to tool execution")
                return "tools"
    
    # Handle completion
    if status == AgentStatus.COMPLETED:
        print(f"🔀 [GRAPH-ROUTER] Agent completed, ending conversation")
        logger.info("Agent completed, ending conversation")
        return END

    # Default: continue processing
    print(f"🔀 [GRAPH-ROUTER] Default routing - ending conversation")
    logger.info("Continuing agent processing")
    return END


def should_continue_after_tools(state: LeadAgentState):
    """
    Determine next step after tool execution.
    """
    status = state.get("status", AgentStatus.IDLE)
    last_tool_result = state.get("last_tool_result")
    
    # Check if tool execution failed
    if last_tool_result and not last_tool_result.success:
        logger.warning("Tool execution failed, routing to error recovery")
        return "error_recovery"
    
    # Check if we're in error state
    if status == AgentStatus.ERROR:
        logger.warning("Error state after tools, routing to error recovery")
        return "error_recovery"
    
    # Continue with agent processing
    logger.info("Tool execution successful, continuing with agent")
    return "agent"


def should_continue_after_error_recovery(state: LeadAgentState):
    """
    Determine next step after error recovery.
    """
    status = state.get("status", AgentStatus.IDLE)
    retry_count = state.get("retry_count", 0)
    
    # Check if recovery was successful
    if status != AgentStatus.ERROR and retry_count < DEFAULT_CONFIG.max_retries:
        logger.info("Error recovery successful, continuing with agent")
        return "agent"
    
    # Recovery failed or max retries exceeded
    logger.warning("Error recovery failed or max retries exceeded")
    return END


def should_continue_after_validation(state: LeadAgentState):
    """
    Determine next step after validation.
    """
    status = state.get("status", AgentStatus.IDLE)
    
    if status == AgentStatus.WAITING_FOR_INPUT:
        # Still waiting for input
        return END
    
    # Validation passed, continue with agent
    logger.info("Validation passed, continuing with agent")
    return "agent"


def create_lead_agent_graph(config=None, enable_memory=True):
    """
    Create the lead agent graph with enhanced workflow and error handling.
    
    Args:
        config: Agent configuration (optional)
        enable_memory: Whether to enable conversation memory (default: True)
        
    Returns:
        Compiled LangGraph workflow
    """
    # Use provided config or default
    agent_config = config or DEFAULT_CONFIG
    
    # Create the state graph
    workflow = StateGraph(LeadAgentState)
    
    # Add nodes
    workflow.add_node("agent", agent_node.execute)
    workflow.add_node("tools", tool_execution_node.execute)
    workflow.add_node("error_recovery", error_recovery_node.execute)
    workflow.add_node("validation", validation_node.execute)
    
    # Set entry point
    workflow.set_entry_point("agent")
    
    # Add conditional edges from agent
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "tools": "tools",
            "error_recovery": "error_recovery",
            "validation": "validation",
            END: END,
        }
    )
    
    # Add conditional edges from tools
    workflow.add_conditional_edges(
        "tools",
        should_continue_after_tools,
        {
            "agent": "agent",
            "error_recovery": "error_recovery",
            END: END,
        }
    )
    
    # Add conditional edges from error recovery
    workflow.add_conditional_edges(
        "error_recovery",
        should_continue_after_error_recovery,
        {
            "agent": "agent",
            END: END,
        }
    )
    
    # Add conditional edges from validation
    workflow.add_conditional_edges(
        "validation",
        should_continue_after_validation,
        {
            "agent": "agent",
            END: END,
        }
    )
    
    # Compile the graph
    if enable_memory and agent_config.enable_memory:
        # Add memory for conversation persistence
        memory = MemorySaver()
        graph = workflow.compile(checkpointer=memory)
        logger.info("Lead agent graph compiled with memory enabled")
    else:
        graph = workflow.compile()
        logger.info("Lead agent graph compiled without memory")
    
    return graph


# Create default graph instance
default_lead_agent_graph = create_lead_agent_graph()


async def run_lead_agent(
    user_input: str,
    context: dict = None,
    config: dict = None,
    thread_id: str = "default"
) -> dict:
    """
    Convenience function to run the lead agent with user input.
    
    Args:
        user_input: User's input message
        context: Optional context information
        config: Optional configuration overrides
        thread_id: Thread ID for conversation memory
        
    Returns:
        Final state after agent execution
    """
    try:
        # Create initial state
        from .state import AgentContext
        agent_context = AgentContext(**(context or {}))
        initial_state = create_initial_state(
            user_input=user_input,
            context=agent_context,
            config=config
        )
        
        # Run the agent
        final_state = await default_lead_agent_graph.ainvoke(
            initial_state,
            config={"configurable": {"thread_id": thread_id}}
        )
        
        return final_state
        
    except Exception as e:
        logger.error(f"Error running lead agent: {str(e)}")
        return {
            "status": AgentStatus.ERROR,
            "error_message": str(e),
            "agent_response": f"I encountered an error: {str(e)}"
        }
