"""
Get Current Time Tool

Simple utility tool for getting the current date and time.
"""

import logging
from langchain_core.tools import tool

from ..action_handlers import get_current_time as _get_current_time

logger = logging.getLogger(__name__)


@tool
def get_current_time() -> str:
    """
    Get the current date and time.
    
    Returns:
        Current date and time as a formatted string
    """
    try:
        return _get_current_time()
    except Exception as e:
        logger.error(f"Error getting current time: {str(e)}")
        return f"Error getting current time: {str(e)}"
