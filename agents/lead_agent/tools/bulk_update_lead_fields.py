"""
Bulk Update Lead Fields Tool

Tool for performing bulk updates on multiple lead fields atomically.
"""

import logging
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
from langchain_core.tools import tool

from ..action_handlers import bulk_update_lead_fields_handler
from ..coagent_integration import get_lead_id_from_state

logger = logging.getLogger(__name__)


class ToolExecutionError(Exception):
    """Custom exception for tool execution errors."""
    pass


async def execute_with_timeout(coro, timeout_seconds: int = 30):
    """Execute coroutine with timeout."""
    try:
        return await asyncio.wait_for(coro, timeout=timeout_seconds)
    except asyncio.TimeoutError:
        raise ToolExecutionError(f"Operation timed out after {timeout_seconds} seconds")


def log_tool_execution(tool_name: str, args: Dict[str, Any], result: Any, execution_time: float):
    """Log tool execution for monitoring."""
    logger.info(
        f"Tool executed: {tool_name} | "
        f"Args: {args} | "
        f"Success: {result.get('success', False) if isinstance(result, dict) else True} | "
        f"Time: {execution_time:.3f}s"
    )


@tool
async def bulk_update_lead_fields(
    lead_id: Optional[str] = None,
    updates: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Perform bulk updates on multiple lead fields atomically.
    The lead ID can be provided or will be automatically retrieved from the current context.
    
    Args:
        lead_id: Unique identifier for the lead (optional if available in context)
        updates: Dictionary containing all field updates to apply
        
    Returns:
        Dictionary containing success status and updated information
    """
    start_time = datetime.now()
    
    try:
        # Get leadId from CoAgent state if not provided
        if not lead_id:
            lead_id = get_lead_id_from_state()

        # Validate that lead_id is available
        if not lead_id:
            return {
                "success": False,
                "error": "Lead ID is required but not provided",
                "message": "Please provide a lead ID or ensure the lead context is properly set"
            }
        
        # Default to empty dict if no updates provided
        if updates is None:
            updates = {}
        
        # Execute with timeout
        result = await execute_with_timeout(
            bulk_update_lead_fields_handler({
                "leadId": lead_id,
                "updates": updates
            })
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        log_tool_execution("bulk_update_lead_fields", {"lead_id": lead_id, "update_categories": len(updates)}, result, execution_time)
        
        return result
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        error_result = {
            "success": False,
            "error": str(e),
            "message": "Failed to perform bulk update"
        }
        log_tool_execution("bulk_update_lead_fields", {"lead_id": lead_id}, error_result, execution_time)
        return error_result
