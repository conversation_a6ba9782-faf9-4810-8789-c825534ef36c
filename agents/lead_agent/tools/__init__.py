"""
Lead Agent Tools

This module contains all the tools available to the lead management agent.
Now simplified to use a single unified update_details tool.
"""

from .update_details import update_details
from .get_current_time import get_current_time

# List of all available tools
LEAD_AGENT_TOOLS = [
    get_current_time,
    update_details
]


def get_tool_by_name(tool_name: str):
    """Get a tool by its name."""
    tool_map = {tool.name: tool for tool in LEAD_AGENT_TOOLS}
    return tool_map.get(tool_name)


def get_all_tools():
    """Get all available tools."""
    return LEAD_AGENT_TOOLS


__all__ = [
    'LEAD_AGENT_TOOLS',
    'update_details',
    'get_current_time',
    'get_tool_by_name',
    'get_all_tools'
]
