"""
Lead Agent State Management

Enhanced state management for the lead agent with better typing,
validation, and state tracking capabilities.
"""

from typing import Annotated, List, Dict, Any, Optional, Union
from typing_extensions import TypedDict
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class AgentStatus(str, Enum):
    """Agent execution status."""
    IDLE = "idle"
    THINKING = "thinking"
    EXECUTING_TOOL = "executing_tool"
    WAITING_FOR_INPUT = "waiting_for_input"
    ERROR = "error"
    COMPLETED = "completed"


class ToolExecutionResult(BaseModel):
    """Result of tool execution."""
    tool_name: str
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ValidationResult(BaseModel):
    """Result of data validation."""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    cleaned_data: Optional[Dict[str, Any]] = None


class AgentContext(BaseModel):
    """Context information for the agent."""
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    current_lead_id: Optional[str] = None
    last_operation: Optional[str] = None
    operation_count: int = 0
    
    class Config:
        arbitrary_types_allowed = True


class AgentMetrics(BaseModel):
    """Metrics and performance tracking."""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    last_operation_time: Optional[datetime] = None
    
    def update_metrics(self, success: bool, execution_time: float):
        """Update metrics with new operation result."""
        self.total_operations += 1
        self.total_execution_time += execution_time
        self.average_execution_time = self.total_execution_time / self.total_operations
        self.last_operation_time = datetime.utcnow()
        
        if success:
            self.successful_operations += 1
        else:
            self.failed_operations += 1


class LeadAgentState(TypedDict):
    """Enhanced state for the lead management agent."""
    
    # Core message handling
    messages: Annotated[List[BaseMessage], add_messages]
    
    # Agent status and control
    status: AgentStatus
    current_step: str
    iteration_count: int
    
    # User input and agent response
    user_input: str
    agent_response: str
    
    # Context and authentication
    context: AgentContext
    
    # Tool execution tracking
    tool_results: List[ToolExecutionResult]
    last_tool_result: Optional[ToolExecutionResult]
    
    # Validation and error handling
    validation_result: Optional[ValidationResult]
    error_message: Optional[str]
    retry_count: int
    
    # Performance metrics
    metrics: AgentMetrics
    
    # Lead-specific data
    current_lead_data: Optional[Dict[str, Any]]
    pending_updates: Optional[Dict[str, Any]]
    
    # Memory and conversation tracking
    conversation_summary: str
    important_facts: List[str]
    
    # Configuration
    config: Optional[Dict[str, Any]]


def create_initial_state(
    user_input: str = "",
    context: Optional[AgentContext] = None,
    config: Optional[Dict[str, Any]] = None
) -> LeadAgentState:
    """Create initial state for the lead agent."""
    return LeadAgentState(
        messages=[],
        status=AgentStatus.IDLE,
        current_step="initialize",
        iteration_count=0,
        user_input=user_input,
        agent_response="",
        context=context or AgentContext(),
        tool_results=[],
        last_tool_result=None,
        validation_result=None,
        error_message=None,
        retry_count=0,
        metrics=AgentMetrics(),
        current_lead_data=None,
        pending_updates=None,
        conversation_summary="",
        important_facts=[],
        config=config or {}
    )


def update_state_status(state: LeadAgentState, status: AgentStatus, step: str = "") -> LeadAgentState:
    """Update agent status and current step."""
    updated_state = state.copy()
    updated_state["status"] = status
    if step:
        updated_state["current_step"] = step
    return updated_state


def add_tool_result(state: LeadAgentState, result: ToolExecutionResult) -> LeadAgentState:
    """Add tool execution result to state."""
    updated_state = state.copy()
    updated_state["tool_results"].append(result)
    updated_state["last_tool_result"] = result
    
    # Update metrics
    metrics = updated_state["metrics"]
    metrics.update_metrics(result.success, result.execution_time or 0.0)
    
    return updated_state


def set_error(state: LeadAgentState, error_message: str) -> LeadAgentState:
    """Set error state."""
    updated_state = state.copy()
    updated_state["status"] = AgentStatus.ERROR
    updated_state["error_message"] = error_message
    updated_state["retry_count"] += 1
    return updated_state


def clear_error(state: LeadAgentState) -> LeadAgentState:
    """Clear error state."""
    updated_state = state.copy()
    updated_state["status"] = AgentStatus.IDLE
    updated_state["error_message"] = None
    return updated_state


def increment_iteration(state: LeadAgentState) -> LeadAgentState:
    """Increment iteration count."""
    updated_state = state.copy()
    updated_state["iteration_count"] += 1
    return updated_state
