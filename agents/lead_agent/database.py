"""
Database connection and operations for Lead Management

This module handles all database operations for lead management actions
using Prisma client and proper authentication/authorization.
"""

import asyncio
import os
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
import re

# Import Prisma client
from prisma import Prisma

# Validation patterns
EMAIL_REGEX = re.compile(r'^[^\s@]+@[^\s@]+\.[^\s@]+$')
URL_REGEX = re.compile(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$')

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and operations for lead management."""

    def __init__(self):
        self._connected = False
        self._client = None
        # Initialize database connection configuration
        self._database_url = os.getenv("DATABASE_URL")
        if not self._database_url:
            logger.error("DATABASE_URL environment variable is required")
            raise ValueError("DATABASE_URL environment variable must be set")

        # Initialize Prisma client
        self._client = Prisma()
        logger.info("Database manager initialized with Prisma client")
        print(f"💾 [DATABASE] Database manager initialized with Prisma client")

    async def connect(self):
        """Connect to the MongoDB database using Prisma."""
        if not self._connected and self._client:
            try:
                await self._client.connect()
                self._connected = True
                print(f"💾 [DATABASE] Prisma database connection established")
                logger.info("Database connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to database: {str(e)}")
                print(f"❌ [DATABASE] Failed to connect: {str(e)}")
                raise

    async def disconnect(self):
        """Disconnect from the database."""
        if self._connected and self._client:
            try:
                await self._client.disconnect()
                self._connected = False
                print(f"💾 [DATABASE] Database connection closed")
                logger.info("Database connection closed")
            except Exception as e:
                logger.error(f"Error disconnecting from database: {str(e)}")
                print(f"❌ [DATABASE] Error disconnecting: {str(e)}")

    def _is_valid_object_id(self, object_id: str) -> bool:
        """Check if a string is a valid MongoDB ObjectId format."""
        if not object_id or not isinstance(object_id, str):
            return False

        # MongoDB ObjectId is 24 characters long and contains only hex characters
        if len(object_id) != 24:
            return False

        try:
            int(object_id, 16)  # Try to parse as hexadecimal
            return True
        except ValueError:
            return False

    async def get_lead_by_id(self, lead_id: str, tenant_id: str = None) -> Optional[Dict[str, Any]]:
        """Get lead by ID with optional tenant validation."""
        await self.connect()

        if not lead_id:
            raise ValueError("Lead ID is required")

        try:
            # Build where clause
            where_clause = {
                "id": lead_id,
                "deleted": False
            }

            # Only add tenant validation if tenant_id looks like a valid ObjectId
            if tenant_id and self._is_valid_object_id(tenant_id):
                where_clause["tenantId"] = tenant_id
            elif tenant_id:
                logger.warning(f"Invalid tenant_id format: {tenant_id}, skipping tenant validation")

            # Query lead with user (owner) information
            lead = await self._client.lead.find_unique(
                where=where_clause,
                include={
                    "user": True  # Include the full user object instead of select
                }
            )

            if lead:
                # Convert to dict and return
                lead_dict = lead.dict()
                logger.info(f"Found lead: {lead_id} for tenant: {lead_dict.get('tenantId')}")
                return lead_dict
            else:
                logger.warning(f"Lead not found: {lead_id}")
                return None

        except Exception as e:
            logger.error(f"Error fetching lead {lead_id}: {e}")
            raise
    
    async def update_lead_basic_info(
        self,
        lead_id: str,
        tenant_id: str,
        user_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update lead basic information with transaction safety."""
        await self.connect()

        if not lead_id or not tenant_id or not user_id:
            raise ValueError("Lead ID, Tenant ID, and User ID are required")

        try:
            # TODO: Implement actual database update using Prisma or preferred ORM
            # Example:
            # updated_lead = await self._client.lead.update(
            #     where={
            #         "id": lead_id,
            #         "tenantId": tenant_id,
            #         "deleted": False
            #     },
            #     data={
            #         **update_data,
            #         "updatedById": user_id,
            #         "updatedAt": datetime.now()
            #     }
            # )
            # return {
            #     "success": True,
            #     "message": "Lead updated successfully",
            #     "updated_lead": updated_lead.dict()
            # }

            # For now, raise an error to indicate this needs implementation
            raise NotImplementedError(
                "Database operations require proper implementation. "
                "Please configure DATABASE_URL and implement Prisma client integration."
            )

        except Exception as e:
            logger.error(f"Error updating lead {lead_id}: {str(e)}")
            raise
    
    async def update_lead_business_info(
        self,
        lead_id: str,
        tenant_id: str,
        user_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update lead business information with transaction safety."""
        await self.connect()

        if not lead_id or not tenant_id or not user_id:
            raise ValueError("Lead ID, Tenant ID, and User ID are required")

        try:
            # TODO: Implement actual database update using Prisma or preferred ORM
            # Similar to update_lead_basic_info but for business fields
            raise NotImplementedError(
                "Database operations require proper implementation. "
                "Please configure DATABASE_URL and implement Prisma client integration."
            )

        except Exception as e:
            logger.error(f"Error updating lead business info {lead_id}: {str(e)}")
            raise
    
    async def get_custom_field_definitions(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get all custom field definitions for Lead entity."""
        await self.connect()

        if not tenant_id:
            raise ValueError("Tenant ID is required")

        try:
            # TODO: Implement actual database query using Prisma or preferred ORM
            # Example:
            # custom_fields = await self._client.customField.find_many(
            #     where={
            #         "tenantId": tenant_id,
            #         "entityType": "Lead"
            #     }
            # )
            # return [field.dict() for field in custom_fields]

            # For now, raise an error to indicate this needs implementation
            raise NotImplementedError(
                "Database operations require proper implementation. "
                "Please configure DATABASE_URL and implement Prisma client integration."
            )

        except Exception as e:
            logger.error(f"Error fetching custom field definitions for tenant {tenant_id}: {str(e)}")
            raise
    
    def validate_email(self, email: str) -> bool:
        """Validate email format."""
        return bool(EMAIL_REGEX.match(email))
    
    def validate_url(self, url: str) -> bool:
        """Validate URL format."""
        return bool(URL_REGEX.match(url))
    
    def clean_phone_number(self, phone: str) -> str:
        """Clean phone number by removing non-numeric characters except + - ( ) space."""
        return re.sub(r'[^\d+\-\s()]', '', phone)

    async def update_lead_custom_fields(
        self,
        lead_id: str,
        tenant_id: str,
        user_id: str,
        validated_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update lead custom fields with transaction safety."""
        await self.connect()

        if not lead_id or not tenant_id or not user_id:
            raise ValueError("Lead ID, Tenant ID, and User ID are required")

        try:
            # TODO: Implement actual database update using Prisma or preferred ORM
            # Example:
            # for field_id, field_data in validated_data.items():
            #     await self._client.customFieldValue.upsert(
            #         where={
            #             "leadId_fieldId": {
            #                 "leadId": lead_id,
            #                 "fieldId": field_id
            #             }
            #         },
            #         update={"value": field_data['value']},
            #         create={
            #             "leadId": lead_id,
            #             "fieldId": field_id,
            #             "value": field_data['value'],
            #             "createdById": user_id
            #         }
            #     )

            # For now, raise an error to indicate this needs implementation
            raise NotImplementedError(
                "Database operations require proper implementation. "
                "Please configure DATABASE_URL and implement Prisma client integration."
            )

        except Exception as e:
            logger.error(f"Error updating custom fields for lead {lead_id}: {str(e)}")
            raise

    async def bulk_update_lead_fields(
        self,
        lead_id: str,
        tenant_id: str,
        user_id: str,
        basic_info: Optional[Dict[str, Any]] = None,
        business_info: Optional[Dict[str, Any]] = None,
        custom_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform bulk update of lead fields with transaction safety."""
        await self.connect()

        if not lead_id:
            raise ValueError("Lead ID is required")

        try:
            print(f"💾 [DATABASE] Starting bulk update for lead {lead_id}")
            print(f"💾 [DATABASE] Basic info updates: {basic_info}")
            print(f"💾 [DATABASE] Business info updates: {business_info}")
            print(f"💾 [DATABASE] Custom fields updates: {custom_fields}")

            # Prepare update data for Prisma - only the fields being updated
            update_data = {
                "updatedAt": datetime.now()
            }

            # Add basic info fields
            if basic_info:
                for key, value in basic_info.items():
                    update_data[key] = value

            # Add business info fields
            if business_info:
                for key, value in business_info.items():
                    update_data[key] = value

            print(f"💾 [DATABASE] Prisma update data: {update_data}")

            # Update lead using Prisma - just by ID
            updated_lead = await self._client.lead.update(
                where={
                    "id": lead_id
                },
                data=update_data
            )

            print(f"✅ [DATABASE] Lead updated successfully in database")

            # Handle custom fields separately if provided
            if custom_fields:
                print(f"💾 [DATABASE] Updating custom fields...")
                # TODO: Implement custom fields update when CustomFieldValue model is available
                # For now, just log that custom fields were requested
                print(f"ℹ️ [DATABASE] Custom fields update requested but not yet implemented")

            # Prepare response
            updated_categories = []
            total_changes = 0
            changes = {}

            if basic_info:
                updated_categories.append("basic_info")
                total_changes += len(basic_info)
                changes["basic_info"] = basic_info
                print(f"✅ [DATABASE] Updated basic info fields: {list(basic_info.keys())}")

            if business_info:
                updated_categories.append("business_info")
                total_changes += len(business_info)
                changes["business_info"] = business_info
                print(f"✅ [DATABASE] Updated business info fields: {list(business_info.keys())}")

            if custom_fields:
                updated_categories.append("custom_fields")
                total_changes += len(custom_fields)
                changes["custom_fields"] = custom_fields
                print(f"✅ [DATABASE] Custom fields processed: {list(custom_fields.keys())}")

            result = {
                "message": f"Successfully updated {total_changes} field(s) for lead {lead_id}",
                "updated_categories": updated_categories,
                "total_changes": total_changes,
                "changes": changes,
                "lead_id": lead_id,
                "updated_at": updated_lead.updatedAt.isoformat() if updated_lead.updatedAt else datetime.now().isoformat()
            }

            print(f"✅ [DATABASE] Bulk update completed successfully")
            return result

        except Exception as e:
            logger.error(f"Error performing bulk update for lead {lead_id}: {str(e)}")
            print(f"❌ [DATABASE] Bulk update failed: {str(e)}")
            raise

# Global database manager instance
db_manager = DatabaseManager()
