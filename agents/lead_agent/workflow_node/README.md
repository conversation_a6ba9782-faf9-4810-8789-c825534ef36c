# Workflow Node Package

This package contains individual workflow nodes for the lead agent system. Each node is implemented as a separate file for better organization, maintainability, and testability.

## Architecture

The workflow node system follows a modular architecture where each node is responsible for a specific aspect of the lead management workflow:

```
workflow_node/
├── __init__.py              # Package initialization and exports
├── README.md               # This documentation
├── base_node.py            # Base class for all nodes
├── agent_node.py           # Main agent processing node
├── tool_execution_node.py  # Tool execution and management
├── error_recovery_node.py  # Error handling and recovery
├── validation_node.py      # Input and data validation
└── workflow_manager.py     # Central workflow management
```

## Node Types

### BaseNode
The foundation class that all workflow nodes inherit from. Provides:
- Common configuration management
- LLM initialization with error handling
- Message preparation utilities
- Logging infrastructure
- Execution lifecycle management

### AgentNode
The main agent processing node that handles:
- User input processing and context management
- LLM interaction with tool binding
- Response generation
- State updates and iteration tracking
- Error recovery and retry logic

### ToolExecutionNode
Manages tool execution with:
- LangGraph ToolNode integration
- Result tracking and metrics
- Error handling and recovery
- Performance monitoring
- State updates after tool execution

### ErrorRecoveryNode
Handles error states and recovery with:
- Error analysis and categorization
- Intelligent retry logic with different strategies
- State cleanup and recovery
- User-friendly error messaging
- Recovery strategy selection

### ValidationNode
Ensures data integrity through:
- User input validation
- Data format verification
- Business rule validation
- State consistency checks
- Comprehensive validation reporting

### WorkflowManager
Central coordination system providing:
- Node registration and management
- Configuration management
- Lifecycle management
- Performance monitoring
- Health checks and diagnostics

## Usage

### Basic Usage

```python
from workflow_node import (
    agent_node,
    tool_execution_node,
    error_recovery_node,
    validation_node,
    workflow_manager
)

# Execute a specific node
result_state = await agent_node.execute(current_state)

# Use the workflow manager
result_state = await workflow_manager.execute_node("agent", current_state)
```

### Integration with LangGraph

```python
from langgraph.graph import StateGraph
from workflow_node import agent_node, tool_execution_node

# Create workflow
workflow = StateGraph(LeadAgentState)

# Add nodes using the execute methods
workflow.add_node("agent", agent_node.execute)
workflow.add_node("tools", tool_execution_node.execute)

# Compile and use
graph = workflow.compile()
```

### Configuration

Each node can be configured independently:

```python
from workflow_node import workflow_manager

# Configure a specific node
workflow_manager.configure_node("agent", {
    "temperature": 0.5,
    "max_tokens": 1500
})

# Get node statistics
stats = workflow_manager.get_stats()
print(f"Total executions: {stats['total_executions']}")
```

### Health Monitoring

```python
# Perform health check
health = workflow_manager.health_check()
print(f"Overall status: {health['overall_status']}")

# Check individual node health
for node_name, node_health in health['nodes'].items():
    print(f"{node_name}: {node_health['status']}")
```

## Error Handling

The workflow node system implements comprehensive error handling:

1. **Node-level error handling**: Each node catches and handles its own errors
2. **Recovery strategies**: The ErrorRecoveryNode implements multiple recovery strategies
3. **Graceful degradation**: Nodes can operate in degraded mode when services are unavailable
4. **Error propagation**: Errors are properly propagated through the state system

## Performance Monitoring

The WorkflowManager tracks:
- Execution times for each node
- Success/failure rates
- Average performance metrics
- Node-specific statistics

## Testing

Each node can be tested independently:

```python
import pytest
from workflow_node import agent_node
from agents.lead_agent.state import create_initial_state

@pytest.mark.asyncio
async def test_agent_node():
    initial_state = create_initial_state(user_input="Test input")
    result_state = await agent_node.execute(initial_state)
    assert result_state["status"] != "error"
```

## Migration from Previous Architecture

The previous monolithic `LeadAgentNodes` class has been refactored into individual node files:

- `LeadAgentNodes.agent_node()` → `agent_node.execute()`
- `LeadAgentNodes.tool_execution_node()` → `tool_execution_node.execute()`
- `LeadAgentNodes.error_recovery_node()` → `error_recovery_node.execute()`
- `LeadAgentNodes.validation_node()` → `validation_node.execute()`

The graph configuration automatically uses the new node structure without requiring changes to the workflow definition.

## Benefits

1. **Modularity**: Each node is self-contained and independently testable
2. **Maintainability**: Easier to modify and extend individual nodes
3. **Reusability**: Nodes can be reused in different workflows
4. **Performance**: Better monitoring and optimization capabilities
5. **Debugging**: Easier to isolate and debug specific node issues
6. **Scalability**: Nodes can be scaled independently if needed
