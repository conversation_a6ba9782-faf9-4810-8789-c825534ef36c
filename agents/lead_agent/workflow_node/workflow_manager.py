"""
Workflow Manager

Central manager for all workflow nodes with configuration and lifecycle management.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from .base_node import BaseNode
from .agent_node import agent_node
from .tool_execution_node import tool_execution_node
from .error_recovery_node import error_recovery_node
from .validation_node import validation_node

from ..state import LeadAgentState
from ..config import DEFAULT_CONFIG

logger = logging.getLogger(__name__)


class WorkflowManager:
    """
    Central manager for workflow nodes.
    
    This class provides:
    - Node registration and management
    - Configuration management
    - Lifecycle management
    - Performance monitoring
    - Error handling coordination
    """
    
    def __init__(self, config=None):
        """Initialize the workflow manager."""
        self.config = config or DEFAULT_CONFIG
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Register all nodes
        self.nodes: Dict[str, BaseNode] = {
            "agent": agent_node,
            "tools": tool_execution_node,
            "error_recovery": error_recovery_node,
            "validation": validation_node
        }
        
        # Performance tracking
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "node_stats": {}
        }
        
        self.logger.info(f"Workflow manager initialized with {len(self.nodes)} nodes")
    
    async def execute_node(self, node_name: str, state: LeadAgentState) -> LeadAgentState:
        """
        Execute a specific node with performance tracking.
        
        Args:
            node_name: Name of the node to execute
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        if node_name not in self.nodes:
            error_msg = f"Unknown node: {node_name}"
            self.logger.error(error_msg)
            from ..state import set_error
            return set_error(state, error_msg)
        
        node = self.nodes[node_name]
        start_time = datetime.now(timezone.utc)
        
        try:
            self.logger.info(f"Executing node: {node_name}")
            
            # Execute the node
            result_state = await node.execute(state)
            
            # Track performance
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            self._update_stats(node_name, True, execution_time)
            
            self.logger.info(f"Node {node_name} executed successfully in {execution_time:.2f}s")
            return result_state
            
        except Exception as e:
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            self._update_stats(node_name, False, execution_time)
            
            self.logger.error(f"Node {node_name} execution failed: {str(e)}")
            from ..state import set_error
            return set_error(state, f"Node {node_name} failed: {str(e)}")
    
    def get_node(self, node_name: str) -> Optional[BaseNode]:
        """Get a specific node by name."""
        return self.nodes.get(node_name)
    
    def list_nodes(self) -> List[str]:
        """Get list of all registered node names."""
        return list(self.nodes.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        return self.execution_stats.copy()
    
    def reset_stats(self) -> None:
        """Reset execution statistics."""
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "node_stats": {}
        }
        self.logger.info("Execution statistics reset")
    
    def configure_node(self, node_name: str, config: Dict[str, Any]) -> bool:
        """
        Configure a specific node.
        
        Args:
            node_name: Name of the node to configure
            config: Configuration dictionary
            
        Returns:
            True if configuration was successful
        """
        if node_name not in self.nodes:
            self.logger.error(f"Cannot configure unknown node: {node_name}")
            return False
        
        try:
            node = self.nodes[node_name]
            # Update node configuration
            for key, value in config.items():
                if hasattr(node.config, key):
                    setattr(node.config, key, value)
            
            self.logger.info(f"Node {node_name} configured successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to configure node {node_name}: {str(e)}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all nodes.
        
        Returns:
            Health status for each node
        """
        health_status = {
            "overall_status": "healthy",
            "nodes": {},
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        for node_name, node in self.nodes.items():
            try:
                # Basic health check - ensure node is properly initialized
                node_health = {
                    "status": "healthy",
                    "config_valid": hasattr(node, 'config') and node.config is not None,
                    "logger_valid": hasattr(node, 'logger') and node.logger is not None
                }
                
                # Check specific node requirements
                if hasattr(node, 'llm'):
                    node_health["llm_available"] = node.llm is not None
                
                if hasattr(node, 'tool_node'):
                    node_health["tools_available"] = node.tool_node is not None
                
                health_status["nodes"][node_name] = node_health
                
            except Exception as e:
                health_status["nodes"][node_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health_status["overall_status"] = "degraded"
        
        return health_status
    
    def _update_stats(self, node_name: str, success: bool, execution_time: float) -> None:
        """Update execution statistics."""
        # Update overall stats
        self.execution_stats["total_executions"] += 1
        
        if success:
            self.execution_stats["successful_executions"] += 1
        else:
            self.execution_stats["failed_executions"] += 1
        
        # Update average execution time
        total_time = (self.execution_stats["average_execution_time"] * 
                     (self.execution_stats["total_executions"] - 1) + execution_time)
        self.execution_stats["average_execution_time"] = total_time / self.execution_stats["total_executions"]
        
        # Update node-specific stats
        if node_name not in self.execution_stats["node_stats"]:
            self.execution_stats["node_stats"][node_name] = {
                "executions": 0,
                "successes": 0,
                "failures": 0,
                "average_time": 0.0
            }
        
        node_stats = self.execution_stats["node_stats"][node_name]
        node_stats["executions"] += 1
        
        if success:
            node_stats["successes"] += 1
        else:
            node_stats["failures"] += 1
        
        # Update node average time
        total_node_time = (node_stats["average_time"] * (node_stats["executions"] - 1) + execution_time)
        node_stats["average_time"] = total_node_time / node_stats["executions"]


# Create global workflow manager instance
workflow_manager = WorkflowManager()
