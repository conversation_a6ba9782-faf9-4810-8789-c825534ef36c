"""
Validation Node

Handle input validation and data verification.
This node ensures data integrity and validates user inputs before processing.
"""

import logging
from typing import List

from .base_node import BaseNode
from ..state import (
    LeadAgentState,
    AgentStatus,
    ValidationResult,
    update_state_status,
    set_error
)

logger = logging.getLogger(__name__)


class ValidationNode(BaseNode):
    """
    Handle input validation and data verification.
    
    This node handles:
    - User input validation
    - Data format verification
    - Business rule validation
    - State consistency checks
    """
    
    def __init__(self, config=None):
        """Initialize the validation node."""
        super().__init__(config)
        self.logger.info("Validation node initialized")
    
    async def execute(self, state: LeadAgentState) -> LeadAgentState:
        """
        Execute the validation node's main functionality.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state with validation results
        """
        try:
            self._log_execution_start(state)
            
            # Update status
            state = update_state_status(state, AgentStatus.THINKING, "validating_input")
            
            # Perform various validations
            validation_results = []
            
            # Validate user input
            input_validation = self._validate_user_input(state)
            validation_results.append(input_validation)
            
            # Validate context
            context_validation = self._validate_context(state)
            validation_results.append(context_validation)
            
            # Validate state consistency
            state_validation = self._validate_state_consistency(state)
            validation_results.append(state_validation)
            
            # Validate pending updates if any
            if state.get("pending_updates"):
                update_validation = self._validate_pending_updates(state)
                validation_results.append(update_validation)
            
            # Combine validation results
            overall_result = self._combine_validation_results(validation_results)
            
            # Update state with validation result
            updated_state = state.copy()
            updated_state["validation_result"] = overall_result
            
            if overall_result.is_valid:
                updated_state = update_state_status(updated_state, AgentStatus.COMPLETED, "validation_passed")
                self.logger.info("All validations passed")
            else:
                error_msg = f"Validation failed: {', '.join(overall_result.errors)}"
                updated_state = set_error(updated_state, error_msg)
                self.logger.warning(f"Validation failed: {overall_result.errors}")
            
            self._log_execution_end(updated_state, success=overall_result.is_valid)
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error in validation_node: {str(e)}")
            error_state = set_error(state, f"Validation failed: {str(e)}")
            self._log_execution_end(error_state, success=False)
            return error_state
    
    def _validate_user_input(self, state: LeadAgentState) -> ValidationResult:
        """Validate user input."""
        user_input = state.get("user_input", "")
        errors = []
        warnings = []
        
        if not user_input or not user_input.strip():
            errors.append("User input is empty or missing")
        elif len(user_input.strip()) < 3:
            warnings.append("User input is very short")
        elif len(user_input) > 5000:
            errors.append("User input is too long (max 5000 characters)")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_type="user_input"
        )
    
    def _validate_context(self, state: LeadAgentState) -> ValidationResult:
        """Validate agent context."""
        context = state.get("context")
        errors = []
        warnings = []
        
        if not context:
            warnings.append("No context provided")
        else:
            if context.current_lead_id and not isinstance(context.current_lead_id, str):
                errors.append("Invalid lead ID format")
            
            if context.tenant_id and not isinstance(context.tenant_id, str):
                errors.append("Invalid tenant ID format")
            
            if context.user_id and not isinstance(context.user_id, str):
                errors.append("Invalid user ID format")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_type="context"
        )
    
    def _validate_state_consistency(self, state: LeadAgentState) -> ValidationResult:
        """Validate state consistency."""
        errors = []
        warnings = []
        
        # Check required fields
        required_fields = ["status", "iteration_count", "messages"]
        for field in required_fields:
            if field not in state:
                errors.append(f"Missing required field: {field}")
        
        # Check iteration count
        iteration_count = state.get("iteration_count", 0)
        if iteration_count < 0:
            errors.append("Invalid iteration count (negative)")
        elif iteration_count > self.config.max_iterations:
            warnings.append("Iteration count approaching maximum")
        
        # Check message consistency
        messages = state.get("messages", [])
        if not isinstance(messages, list):
            errors.append("Messages field must be a list")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_type="state_consistency"
        )
    
    def _validate_pending_updates(self, state: LeadAgentState) -> ValidationResult:
        """Validate pending updates."""
        pending_updates = state.get("pending_updates", {})
        errors = []
        warnings = []
        
        if not isinstance(pending_updates, dict):
            errors.append("Pending updates must be a dictionary")
        else:
            # Validate update fields
            for field, value in pending_updates.items():
                if not isinstance(field, str):
                    errors.append(f"Invalid field name: {field}")
                
                # Add specific field validations here
                if field == "email" and value:
                    if "@" not in str(value):
                        errors.append(f"Invalid email format: {value}")
                
                if field == "phone" and value:
                    # Basic phone validation
                    phone_str = str(value).replace(" ", "").replace("-", "").replace("(", "").replace(")", "")
                    if not phone_str.replace("+", "").isdigit():
                        warnings.append(f"Phone number format may be invalid: {value}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_type="pending_updates"
        )
    
    def _combine_validation_results(self, results: List[ValidationResult]) -> ValidationResult:
        """Combine multiple validation results into one."""
        all_errors = []
        all_warnings = []
        
        for result in results:
            all_errors.extend(result.errors)
            all_warnings.extend(result.warnings)
        
        return ValidationResult(
            is_valid=len(all_errors) == 0,
            errors=all_errors,
            warnings=all_warnings,
            validation_type="combined"
        )


# Create global instance
validation_node = ValidationNode()
