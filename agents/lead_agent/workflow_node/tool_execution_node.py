"""
Tool Execution Node

Enhanced tool execution node with better error handling and result tracking.
This node handles the execution of tools called by the agent.
"""

import logging
from datetime import datetime, timezone
from langgraph.prebuilt import ToolNode

from .base_node import BaseNode
from ..state import (
    LeadAgentState,
    AgentStatus,
    ToolExecutionResult,
    update_state_status,
    add_tool_result,
    set_error
)
from ..tools import LEAD_AGENT_TOOLS

logger = logging.getLogger(__name__)


class ToolExecutionNode(BaseNode):
    """
    Enhanced tool execution node with better error handling and result tracking.
    
    This node handles:
    - Tool execution using LangGraph's ToolNode
    - Result tracking and metrics
    - Error handling and recovery
    - State updates after tool execution
    """
    
    def __init__(self, config=None):
        """Initialize the tool execution node."""
        super().__init__(config)
        
        # Initialize the LangGraph ToolNode
        try:
            self.tool_node = ToolNode(LEAD_AGENT_TOOLS)
            self.logger.info("Tool execution node initialized with available tools")
        except Exception as e:
            self.logger.error(f"Failed to initialize ToolNode: {e}")
            self.tool_node = None
    
    async def execute(self, state: LeadAgentState) -> LeadAgentState:
        """
        Execute the tool execution node's main functionality.

        Args:
            state: Current agent state

        Returns:
            Updated agent state with tool execution results
        """
        print(f"🛠️ [TOOL-NODE] Starting tool execution")
        try:
            self._log_execution_start(state)
            
            # Update status
            state = update_state_status(state, AgentStatus.EXECUTING_TOOL, "executing_tools")
            
            if self.tool_node is None:
                error_msg = "Tool execution is not available - ToolNode not initialized"
                self.logger.error(error_msg)
                return set_error(state, error_msg)
            
            # Execute tools using the built-in ToolNode
            start_time = datetime.now(timezone.utc)
            print(f"🛠️ [TOOL-NODE] Starting tool execution with ToolNode")
            self.logger.info("Starting tool execution")

            result_state = await self.tool_node.ainvoke(state)
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds()

            print(f"🛠️ [TOOL-NODE] Tool execution completed in {execution_time:.2f} seconds")
            self.logger.info(f"Tool execution completed in {execution_time:.2f} seconds")
            
            # Create tool execution result
            tool_result = ToolExecutionResult(
                tool_name="tool_execution_batch",
                success=True,
                result="Tools executed successfully",
                execution_time=execution_time,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Add result to state
            updated_state = add_tool_result(result_state, tool_result)
            updated_state = update_state_status(updated_state, AgentStatus.COMPLETED, "tools_executed")
            
            self._log_execution_end(updated_state, success=True)
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error in tool_execution_node: {str(e)}")
            
            # Create failed tool result
            tool_result = ToolExecutionResult(
                tool_name="tool_execution",
                success=False,
                error=str(e),
                execution_time=0.0,
                timestamp=datetime.now(timezone.utc)
            )
            
            error_state = add_tool_result(state, tool_result)
            error_state = set_error(error_state, f"Tool execution failed: {str(e)}")
            self._log_execution_end(error_state, success=False)
            return error_state


# Create global instance
tool_execution_node = ToolExecutionNode()
