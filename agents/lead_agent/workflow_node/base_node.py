"""
Base Node Class

Base class for all workflow nodes with common functionality and configuration.
"""

import logging
from abc import ABC, abstractmethod
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from ..state import LeadAgentState
from ..config import DEFAULT_CONFIG, SYSTEM_PROMPTS

logger = logging.getLogger(__name__)


class BaseNode(ABC):
    """Base class for all workflow nodes."""
    
    def __init__(self, config=None):
        """Initialize the base node with configuration."""
        self.config = config or DEFAULT_CONFIG
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize LLM with error handling for missing API key
        try:
            self.llm = ChatOpenAI(
                model=self.config.model_name,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            self.logger.info(f"Initialized {self.__class__.__name__} with OpenAI LLM")
        except Exception as e:
            self.logger.warning(f"Failed to initialize OpenAI LLM: {e}")
            self.llm = None
    
    @abstractmethod
    async def execute(self, state: LeadAgentState) -> LeadAgentState:
        """Execute the node's main functionality."""
        pass
    
    def _prepare_messages(self, state: LeadAgentState) -> list:
        """Prepare messages for LLM with proper context and system prompts."""
        messages = state.get("messages", [])
        
        # Add system message if not present
        if not messages or not isinstance(messages[0], SystemMessage):
            system_message = SystemMessage(content=SYSTEM_PROMPTS["main"])
            messages = [system_message] + messages
        
        # Add user input if provided and not already in messages
        user_input = state.get("user_input", "")
        if user_input and not any(isinstance(msg, HumanMessage) and msg.content == user_input for msg in messages):
            messages.append(HumanMessage(content=user_input))
        
        # Add context information
        context = state.get("context")
        if context and context.current_lead_id:
            context_msg = f"\n\nCurrent context: Working with lead ID {context.current_lead_id}"
            if messages and isinstance(messages[-1], HumanMessage):
                messages[-1].content += context_msg
        
        return messages
    
    def _log_execution_start(self, state: LeadAgentState) -> None:
        """Log the start of node execution."""
        iteration = state.get("iteration_count", 0)
        status = state.get("status", "unknown")
        self.logger.info(f"Starting {self.__class__.__name__} execution - Iteration: {iteration}, Status: {status}")
    
    def _log_execution_end(self, state: LeadAgentState, success: bool = True) -> None:
        """Log the end of node execution."""
        status = state.get("status", "unknown")
        if success:
            self.logger.info(f"Completed {self.__class__.__name__} execution - New Status: {status}")
        else:
            error = state.get("error_message", "Unknown error")
            self.logger.error(f"Failed {self.__class__.__name__} execution - Error: {error}")
