"""
Error Recovery Node

Handle errors and attempt recovery with intelligent retry logic.
This node manages error states and attempts to recover from failures.
"""

import logging
from langchain_core.messages import HumanMessage

from .base_node import BaseNode
from ..state import (
    LeadAgentState,
    AgentStatus,
    set_error
)
from ..config import ERROR_MESSAGES

logger = logging.getLogger(__name__)


class ErrorRecoveryNode(BaseNode):
    """
    Handle errors and attempt recovery.
    
    This node handles:
    - Error analysis and categorization
    - Intelligent retry logic
    - State cleanup and recovery
    - User-friendly error messaging
    """
    
    def __init__(self, config=None):
        """Initialize the error recovery node."""
        super().__init__(config)
        self.logger.info("Error recovery node initialized")
    
    async def execute(self, state: LeadAgentState) -> LeadAgentState:
        """
        Execute the error recovery node's main functionality.
        
        Args:
            state: Current agent state with error
            
        Returns:
            Updated agent state after recovery attempt
        """
        try:
            self._log_execution_start(state)
            
            error_message = state.get("error_message", "Unknown error")
            retry_count = state.get("retry_count", 0)
            
            self.logger.warning(f"Error recovery attempt {retry_count + 1}: {error_message}")
            
            # Increment retry count
            updated_state = state.copy()
            updated_state["retry_count"] = retry_count + 1
            
            # Analyze error type and determine recovery strategy
            recovery_strategy = self._analyze_error(error_message)
            self.logger.info(f"Using recovery strategy: {recovery_strategy}")
            
            if recovery_strategy == "retry_with_simpler_prompt":
                # Simplify the user input and retry
                updated_state = self._simplify_and_retry(updated_state)
                
            elif recovery_strategy == "clear_context_and_retry":
                # Clear problematic context and retry
                updated_state = self._clear_context_and_retry(updated_state)
                
            elif recovery_strategy == "provide_helpful_message":
                # Provide a helpful error message to the user
                updated_state = self._provide_helpful_message(updated_state, error_message)
                
            else:
                # Default recovery: provide generic error message
                updated_state = self._default_recovery(updated_state, error_message)
            
            self._log_execution_end(updated_state, success=True)
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error in error_recovery_node: {str(e)}")
            # If error recovery itself fails, return a final error state
            final_error_state = set_error(state, f"Error recovery failed: {str(e)}")
            self._log_execution_end(final_error_state, success=False)
            return final_error_state
    
    def _analyze_error(self, error_message: str) -> str:
        """
        Analyze the error message to determine the best recovery strategy.
        
        Args:
            error_message: The error message to analyze
            
        Returns:
            Recovery strategy identifier
        """
        error_lower = error_message.lower()
        
        if "api key" in error_lower or "authentication" in error_lower:
            return "provide_helpful_message"
        elif "timeout" in error_lower or "connection" in error_lower:
            return "retry_with_simpler_prompt"
        elif "context" in error_lower or "memory" in error_lower:
            return "clear_context_and_retry"
        elif "tool" in error_lower or "function" in error_lower:
            return "retry_with_simpler_prompt"
        else:
            return "default_recovery"
    
    def _simplify_and_retry(self, state: LeadAgentState) -> LeadAgentState:
        """Simplify the request and prepare for retry."""
        updated_state = state.copy()
        
        # Clear error state
        updated_state["error_message"] = None
        updated_state["status"] = AgentStatus.IDLE
        updated_state["current_step"] = "retry_simplified"
        
        # Add a simplified prompt
        simplified_prompt = "Please help me with a simple lead management task."
        updated_state["user_input"] = simplified_prompt
        updated_state["messages"].append(HumanMessage(content=simplified_prompt))
        
        self.logger.info("Prepared simplified retry")
        return updated_state
    
    def _clear_context_and_retry(self, state: LeadAgentState) -> LeadAgentState:
        """Clear problematic context and prepare for retry."""
        updated_state = state.copy()
        
        # Clear error state
        updated_state["error_message"] = None
        updated_state["status"] = AgentStatus.IDLE
        updated_state["current_step"] = "retry_cleared_context"
        
        # Clear problematic context
        if updated_state.get("context"):
            updated_state["context"].current_lead_id = None
            updated_state["context"].last_operation = None
        
        self.logger.info("Cleared context for retry")
        return updated_state
    
    def _provide_helpful_message(self, state: LeadAgentState, error_message: str) -> LeadAgentState:
        """Provide a helpful error message to the user."""
        updated_state = state.copy()
        
        # Get appropriate error message
        helpful_message = ERROR_MESSAGES.get("api_key_missing", 
            f"I encountered an issue: {error_message}. Please check your configuration and try again.")
        
        updated_state["agent_response"] = helpful_message
        updated_state["status"] = AgentStatus.COMPLETED
        updated_state["current_step"] = "error_message_provided"
        
        self.logger.info("Provided helpful error message")
        return updated_state
    
    def _default_recovery(self, state: LeadAgentState, error_message: str) -> LeadAgentState:
        """Default recovery strategy."""
        updated_state = state.copy()
        
        default_message = f"I encountered an unexpected issue: {error_message}. Please try rephrasing your request or contact support if the problem persists."
        
        updated_state["agent_response"] = default_message
        updated_state["status"] = AgentStatus.COMPLETED
        updated_state["current_step"] = "default_recovery_completed"
        
        self.logger.info("Applied default recovery strategy")
        return updated_state


# Create global instance
error_recovery_node = ErrorRecoveryNode()
