"""
Lead CoAgent Implementation

This module implements the backend CoAgent that handles state management
and lead operations from the frontend useCoAgent hook.
"""

from typing import Dict, Any, Optional
from copilotkit import CoAgent, CoAgentState
from .action_handlers import (
    bulk_update_lead_fields_handler
)
from .context import set_context, clear_context
import logging

logger = logging.getLogger(__name__)


class LeadAgentState(CoAgentState):
    """State model for the Lead Agent."""
    
    def __init__(self, **data):
        super().__init__(**data)
        self.tenant_id: Optional[str] = data.get('tenantId')
        self.user_id: Optional[str] = data.get('userId') 
        self.lead_id: Optional[str] = data.get('leadId')
    
    def get(self, key: str, default=None):
        """Get state value by key."""
        return getattr(self, key.replace('Id', '_id'), default)
    
    def set(self, key: str, value: Any):
        """Set state value by key."""
        setattr(self, key.replace('Id', '_id'), value)


class LeadCoAgent(CoAgent):
    """
    Lead Management CoAgent
    
    Handles lead operations with state management from frontend.
    """
    
    def __init__(self):
        super().__init__(
            name="lead_agent",
            description="Lead management agent that handles lead operations with state management"
        )
    
    async def handle_action(self, action: str, args: Dict[str, Any], state: LeadAgentState) -> Dict[str, Any]:
        """
        Handle actions from the frontend with state context.
        
        Args:
            action: The action to perform
            args: Action arguments
            state: Current agent state
            
        Returns:
            Action result
        """
        try:
            # Extract context from state
            tenant_id = state.get('tenantId')
            user_id = state.get('userId')
            lead_id = state.get('leadId')
            
            logger.info(f"Handling action: {action} for lead: {lead_id}")
            
            # Set context for the request
            if tenant_id and user_id:
                set_context(tenant_id, user_id)
            
            # Ensure leadId is in args if available in state
            if lead_id and 'leadId' not in args:
                args['leadId'] = lead_id
            
            # Route to appropriate handler
            if action == 'updateDetails':
                # Use the bulk update handler for the unified action
                result = await bulk_update_lead_fields_handler(args)
            elif action == 'bulkUpdateLead':
                # Keep bulk update for backward compatibility
                result = await bulk_update_lead_fields_handler(args)
            else:
                result = {
                    "success": False,
                    "error": f"Unknown action: {action}. Use 'updateDetails' for all lead updates."
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error handling action {action}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
        finally:
            # Always clear context
            clear_context()
    
    async def update_lead_basic_info(self, state: LeadAgentState, **kwargs) -> Dict[str, Any]:
        """Update lead basic information."""
        return await self.handle_action('updateLeadBasicInfo', kwargs, state)
    
    async def update_lead_business_info(self, state: LeadAgentState, **kwargs) -> Dict[str, Any]:
        """Update lead business information."""
        return await self.handle_action('updateLeadBusinessInfo', kwargs, state)
    
    async def update_lead_custom_fields(self, state: LeadAgentState, **kwargs) -> Dict[str, Any]:
        """Update lead custom fields."""
        return await self.handle_action('updateLeadCustomFields', kwargs, state)
    
    async def bulk_update_lead(self, state: LeadAgentState, **kwargs) -> Dict[str, Any]:
        """Bulk update lead fields."""
        return await self.handle_action('bulkUpdateLead', kwargs, state)


# Global instance
lead_co_agent = LeadCoAgent()


def create_lead_co_agent() -> LeadCoAgent:
    """Create and return the lead CoAgent instance."""
    return lead_co_agent


async def handle_co_agent_request(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle CoAgent requests from the frontend.
    
    Expected request format:
    {
        "action": "updateLeadBasicInfo",
        "args": {...},
        "state": {
            "tenantId": "...",
            "userId": "...", 
            "leadId": "..."
        }
    }
    """
    try:
        action = request_data.get('action')
        args = request_data.get('args', {})
        state_data = request_data.get('state', {})
        
        if not action:
            return {
                "success": False,
                "error": "Action is required"
            }
        
        # Create state object
        state = LeadAgentState(**state_data)
        
        # Handle the action
        result = await lead_co_agent.handle_action(action, args, state)
        
        return result
        
    except Exception as e:
        logger.error(f"Error handling CoAgent request: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
