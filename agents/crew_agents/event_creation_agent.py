"""
Event Creation Agent

This module provides the specialized agent for creating calendar events, meetings,
and reminders based on call notes using LangChain.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from langchain.schema import HumanMessage, SystemMessage

from core.prompt_loader import prompt_loader
from ai_models.azure_openai import get_azure_openai_model

logger = logging.getLogger(__name__)


class EventCreationAgent:
    """Specialized agent for creating calendar events from call notes."""

    def __init__(self):
        """Initialize the event creation agent."""
        self.llm = get_azure_openai_model(
            temperature=0.3,
            max_tokens=3000  # Increased token limit to prevent truncation
        )
        self.system_message = SystemMessage(content="""You are an expert calendar and scheduling specialist with extensive experience
        in business scheduling, meeting coordination, and time management. You excel at identifying
        scheduling opportunities, setting appropriate meeting durations, and creating meaningful
        calendar events that support business objectives. Your scheduling recommendations are known
        for being practical, well-timed, and conducive to productive business relationships.

        Your goal is to create appropriate calendar events, meetings, and reminders based on call notes and scheduling needs.

        CRITICAL: You must respond with valid JSON only. Do not include any explanatory text.""")
    
    async def _generate_event_response(self, note_content: str, lead_context: str = "") -> str:
        """
        Generate event recommendations using LangChain directly.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            str: AI response with event recommendations
        """
        prompt = prompt_loader.get_crew_event_agent_prompt(note_content, lead_context)

        messages = [
            self.system_message,
            HumanMessage(content=prompt)
        ]

        response = await self.llm.create_chat_completion_async(messages)
        return response.content

    async def generate_events(self, note_content: str, lead_context: str = "") -> List[Dict[str, Any]]:
        """
        Generate calendar events based on call notes.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            List[Dict]: List of generated events
        """
        try:
            logger.info("Starting event generation")

            # Generate response using LangChain
            ai_response = await self._generate_event_response(note_content, lead_context)

            # Log the raw AI response for debugging
            logger.debug(f"Raw AI response for events: {ai_response[:500]}...")

            # Check if response is valid
            if not ai_response or len(ai_response.strip()) < 10:
                logger.warning("AI returned empty or very short response for events")
                return []

            # Check for truncated responses (common patterns)
            truncated_patterns = [
                "and end with",
                "Start with {",
                "End with }",
                "No markdown",
                "No explanatory",
                "Valid JSON syntax"
            ]

            if any(pattern in ai_response for pattern in truncated_patterns):
                logger.warning(f"Detected truncated AI response for events: {ai_response}")
                return []

            # Parse the result
            events = self._parse_event_result(ai_response)

            # Print event generation results
            print(f"\n📅 EVENT AGENT RESULTS: Generated {len(events)} events")
            for i, event in enumerate(events, 1):
                print(f"  {i}. {event.get('title', 'No title')} (Type: {event.get('event_type', 'unknown')})")
            print()

            logger.info(f"Generated {len(events)} events")
            return events

        except Exception as e:
            print(f"\n❌ EVENT AGENT ERROR: {type(e).__name__}: {e}")
            logger.error(f"Error generating events: {e}")
            return []
    
    def _parse_event_result(self, result: str) -> List[Dict[str, Any]]:
        """
        Parse the agent result to extract events.

        Args:
            result: Raw result from the agent

        Returns:
            List[Dict]: Parsed events
        """
        try:
            if not result or not isinstance(result, str):
                logger.warning("Empty or invalid result received")
                return []

            # Clean the result string
            result = result.strip()

            # Try multiple JSON extraction strategies
            json_data = None

            # Strategy 1: Look for JSON block markers
            if "```json" in result:
                start_marker = result.find("```json") + 7
                end_marker = result.find("```", start_marker)
                if end_marker != -1:
                    json_str = result[start_marker:end_marker].strip()
                    try:
                        json_data = json.loads(json_str)
                    except json.JSONDecodeError:
                        pass

            # Strategy 2: Look for first complete JSON object
            if json_data is None:
                start_idx = result.find('{')
                if start_idx != -1:
                    # Find the matching closing brace
                    brace_count = 0
                    end_idx = start_idx
                    for i, char in enumerate(result[start_idx:], start_idx):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_idx = i + 1
                                break

                    if brace_count == 0:
                        json_str = result[start_idx:end_idx]
                        try:
                            json_data = json.loads(json_str)
                        except json.JSONDecodeError:
                            pass

            # Strategy 3: Try parsing the entire result as JSON
            if json_data is None:
                try:
                    json_data = json.loads(result)
                except json.JSONDecodeError:
                    pass

            # Extract events from parsed JSON
            if json_data and isinstance(json_data, dict):
                if 'events' in json_data and isinstance(json_data['events'], list):
                    logger.info(f"Successfully parsed {len(json_data['events'])} events")
                    return json_data['events']
                elif isinstance(json_data, list):
                    # If the result is directly a list of events
                    logger.info(f"Successfully parsed {len(json_data)} events from direct list")
                    return json_data
            elif json_data and isinstance(json_data, list):
                # Handle case where json_data is directly a list
                logger.info(f"Successfully parsed {len(json_data)} events from direct list")
                return json_data

            # If no valid JSON found, check if it's a partial response and return empty
            if result and len(result.strip()) > 5:
                # Check if it looks like a partial JSON response
                if '"emails"' in result or '"tasks"' in result or '"events"' in result:
                    logger.warning(f"Detected partial JSON response, returning empty list. Response: {result}")
                    return []

            # If all parsing strategies fail, log the result for debugging
            logger.warning(f"Could not parse event generation result as JSON. Result preview: {result[:200]}...")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}. Result preview: {result[:200] if result else 'None'}...")
            return []
        except Exception as e:
            logger.error(f"Error parsing event result: {e}")
            return []
    
    def _parse_datetime(self, datetime_str: str) -> Optional[datetime]:
        """
        Parse datetime string to datetime object.
        
        Args:
            datetime_str: Datetime string from agent
            
        Returns:
            Optional[datetime]: Parsed datetime or None
        """
        try:
            if not datetime_str:
                return None
                
            # Try to parse ISO format datetime
            if 'T' in datetime_str:
                return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            else:
                # Try date only format and add default time
                date_obj = datetime.strptime(datetime_str, '%Y-%m-%d')
                return date_obj.replace(hour=10, minute=0)  # Default to 10 AM
                
        except (ValueError, TypeError):
            # If parsing fails, default to next business day at 10 AM
            now = datetime.now()
            days_ahead = 1
            if now.weekday() >= 4:  # If Friday or later, schedule for Monday
                days_ahead = 7 - now.weekday()
            return (now + timedelta(days=days_ahead)).replace(hour=10, minute=0, second=0, microsecond=0)
    
    def format_event_for_storage(self, event_data: Dict[str, Any], reference_modal: str, reference_modal_id: str) -> Dict[str, Any]:
        """
        Format event data for database storage.
        
        Args:
            event_data: Raw event data from agent
            reference_modal: Source model type (e.g., "Lead", "Note")
            reference_modal_id: Source model ID
            
        Returns:
            Dict: Formatted event data for database
        """
        start_time = self._parse_datetime(event_data.get("start_time"))
        end_time = self._parse_datetime(event_data.get("end_time"))
        
        # If end_time is not provided, calculate based on duration or default to 1 hour
        if start_time and not end_time:
            duration_minutes = event_data.get("duration_minutes", 60)
            end_time = start_time + timedelta(minutes=duration_minutes)
        
        return {
            "title": event_data.get("title", "Untitled Event"),
            "description": event_data.get("description"),
            "startTime": start_time,
            "endTime": end_time,
            "location": event_data.get("location"),
            "attendees": event_data.get("attendees", []),
            "eventType": event_data.get("event_type", "meeting"),
            "status": "scheduled",
            "reference_modal": reference_modal,
            "reference_modalId": reference_modal_id,
            "deleted": False
        }
