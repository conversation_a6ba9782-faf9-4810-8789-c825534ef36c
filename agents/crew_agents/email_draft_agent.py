"""
Email Draft Agent

This module provides the specialized agent for generating professional email drafts
based on call notes and business context using LangChain.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from langchain.schema import HumanMessage, SystemMessage

from core.prompt_loader import prompt_loader
from ai_models.azure_openai import get_azure_openai_model

logger = logging.getLogger(__name__)


class EmailDraftAgent:
    """Specialized agent for generating professional email drafts."""

    def __init__(self):
        """Initialize the email draft agent."""
        self.llm = get_azure_openai_model(
            temperature=0.3,
            max_tokens=3000  # Increased token limit to prevent truncation
        )
        self.system_message = SystemMessage(content="""You are an expert email communication specialist with extensive experience in
        business correspondence and CRM systems. You excel at crafting professional emails that
        maintain appropriate tone, include relevant context, and drive business objectives forward.
        Your emails are known for being clear, actionable, and relationship-building.

        Your goal is to generate professional, contextually appropriate email drafts based on call notes and business interactions.

        CRITICAL: You must respond with valid JSON only. Do not include any explanatory text.""")
    
    async def _generate_email_response(self, note_content: str, lead_context: str = "") -> str:
        """
        Generate email drafts using LangChain directly.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            str: AI response with email drafts
        """
        prompt = prompt_loader.get_crew_email_agent_prompt(note_content, lead_context)

        messages = [
            self.system_message,
            HumanMessage(content=prompt)
        ]

        response = await self.llm.create_chat_completion_async(messages)
        return response.content
    
    async def generate_emails(self, note_content: str, lead_context: str = "") -> List[Dict[str, Any]]:
        """
        Generate email drafts based on call notes.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            List[Dict]: List of generated email drafts
        """
        try:
            logger.info("Starting email draft generation")

            # Generate response using LangChain
            ai_response = await self._generate_email_response(note_content, lead_context)

            # Log the raw AI response for debugging
            logger.debug(f"Raw AI response for emails: {ai_response[:500]}...")

            # Check if response is valid
            if not ai_response or len(ai_response.strip()) < 10:
                logger.warning("AI returned empty or very short response for emails")
                return []

            # Check for truncated responses (common patterns)
            truncated_patterns = [
                "and end with",
                "Start with {",
                "End with }",
                "No markdown",
                "No explanatory",
                "Valid JSON syntax"
            ]

            if any(pattern in ai_response for pattern in truncated_patterns):
                logger.warning(f"Detected truncated AI response for emails: {ai_response}")
                return []

            # Parse the result
            emails = self._parse_email_result(ai_response)

            # Print email generation results
            print(f"\n📧 EMAIL AGENT RESULTS: Generated {len(emails)} emails")
            for i, email in enumerate(emails, 1):
                print(f"  {i}. {email.get('subject', 'No subject')} (Type: {email.get('type', 'unknown')})")
            print()

            logger.info(f"Generated {len(emails)} email drafts")
            return emails

        except Exception as e:
            print(f"\n❌ EMAIL AGENT ERROR: {type(e).__name__}: {e}")
            logger.error(f"Error generating emails: {e}")
            return []
    
    def _parse_email_result(self, result: str) -> List[Dict[str, Any]]:
        """
        Parse the agent result to extract email drafts.

        Args:
            result: Raw result from the agent

        Returns:
            List[Dict]: Parsed email drafts
        """
        try:
            if not result or not isinstance(result, str):
                logger.warning("Empty or invalid result received")
                return []

            # Clean the result string
            result = result.strip()

            # Try multiple JSON extraction strategies
            json_data = None

            # Strategy 1: Look for JSON block markers
            if "```json" in result:
                start_marker = result.find("```json") + 7
                end_marker = result.find("```", start_marker)
                if end_marker != -1:
                    json_str = result[start_marker:end_marker].strip()
                    try:
                        json_data = json.loads(json_str)
                    except json.JSONDecodeError:
                        pass

            # Strategy 2: Look for first complete JSON object
            if json_data is None:
                start_idx = result.find('{')
                if start_idx != -1:
                    # Find the matching closing brace
                    brace_count = 0
                    end_idx = start_idx
                    for i, char in enumerate(result[start_idx:], start_idx):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_idx = i + 1
                                break

                    if brace_count == 0:
                        json_str = result[start_idx:end_idx]
                        try:
                            json_data = json.loads(json_str)
                        except json.JSONDecodeError:
                            pass

            # Strategy 3: Try parsing the entire result as JSON
            if json_data is None:
                try:
                    json_data = json.loads(result)
                except json.JSONDecodeError:
                    pass

            # Extract emails from parsed JSON
            if json_data and isinstance(json_data, dict):
                if 'emails' in json_data and isinstance(json_data['emails'], list):
                    logger.info(f"Successfully parsed {len(json_data['emails'])} emails")
                    return json_data['emails']
                elif isinstance(json_data, list):
                    # If the result is directly a list of emails
                    logger.info(f"Successfully parsed {len(json_data)} emails from direct list")
                    return json_data
            elif json_data and isinstance(json_data, list):
                # Handle case where json_data is directly a list
                logger.info(f"Successfully parsed {len(json_data)} emails from direct list")
                return json_data

            # If no valid JSON found, check if it's a partial response and return empty
            if result and len(result.strip()) > 5:
                # Check if it looks like a partial JSON response
                if '"emails"' in result or '"tasks"' in result or '"events"' in result:
                    logger.warning(f"Detected partial JSON response, returning empty list. Response: {result}")
                    return []
                else:
                    logger.warning("JSON parsing failed, attempting text-based extraction")
                    return self._extract_emails_from_text(result)

            # If all parsing strategies fail, log the result for debugging
            logger.warning(f"Could not parse email generation result. Result preview: {result[:200]}...")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}. Result preview: {result[:200] if result else 'None'}...")
            return []
        except Exception as e:
            logger.error(f"Error parsing email result: {e}")
            return []

    def _extract_emails_from_text(self, text: str) -> List[Dict[str, Any]]:
        """
        Fallback method to extract email information from text when JSON parsing fails.

        Args:
            text: Raw text response from AI

        Returns:
            List[Dict]: Extracted email data
        """
        try:
            # This is a simple fallback - in a real scenario, you might want more sophisticated parsing
            logger.info("Attempting text-based email extraction as fallback")

            # For now, return an empty list but log that we attempted extraction
            # You could implement regex-based extraction here if needed
            logger.warning("Text-based extraction not implemented, returning empty list")
            return []

        except Exception as e:
            logger.error(f"Error in text-based email extraction: {e}")
            return []

    def format_email_for_storage(self, email_data: Dict[str, Any], reference_modal: str, reference_modal_id: str) -> Dict[str, Any]:
        """
        Format email data for database storage.
        
        Args:
            email_data: Raw email data from agent
            reference_modal: Source model type (e.g., "Lead", "Note")
            reference_modal_id: Source model ID
            
        Returns:
            Dict: Formatted email data for database
        """
        return {
            "from": email_data.get("from"),
            "to": email_data.get("to", []),
            "subject": email_data.get("subject"),
            "htmlContent": email_data.get("content"),
            "status": "draft",
            "reference_modal": reference_modal,
            "reference_modalId": reference_modal_id,
            "deleted": False
        }
