# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Database Configuration (REQUIRED)
DATABASE_URL="********************************:port/database"

# OpenAI Configuration (REQUIRED for AI features)
OPENAI_API_KEY="your-openai-api-key-here"

# Application Configuration
NODE_ENV="production"
PORT=8000

# Authentication (if using JWT)
JWT_SECRET="your-secure-jwt-secret-here"

# CORS Configuration
CORS_ORIGINS="https://yourdomain.com,https://app.yourdomain.com"

# Logging
LOG_LEVEL="INFO"

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_REQUESTS="100"

# CopilotKit Configuration
COPILOTKIT_ENDPOINT_PATH="/copilotkit"

# Security
REQUIRE_AUTH="true"

# Example MongoDB URLs:
# Local: mongodb://localhost:27017/flinkk-crm
# MongoDB Atlas: mongodb+srv://username:<EMAIL>/flinkk-crm
# Docker: mongodb://mongo:27017/flinkk-crm
