"""
CopilotKit Actions Registry

This module provides a centralized registry for all CopilotKit actions across different domains.
Actions are organized by domain (leads, opportunities, tasks, etc.) for better maintainability.
"""

from typing import List
from copilotkit import Action

# Import domain-specific actions
from .leads import lead_actions
from .opportunities import opportunity_actions
from .tasks import task_actions

# Registry of all available actions
ALL_ACTIONS: List[Action] = []

# Add actions from each domain
ALL_ACTIONS.extend(lead_actions)
ALL_ACTIONS.extend(opportunity_actions)
ALL_ACTIONS.extend(task_actions)


def get_all_actions() -> List[Action]:
    """Get all registered CopilotKit actions."""
    return ALL_ACTIONS


def get_actions_by_domain(domain: str) -> List[Action]:
    """Get actions for a specific domain."""
    domain_actions = {
        "leads": lead_actions,
        "opportunities": opportunity_actions,
        "tasks": task_actions,
    }
    return domain_actions.get(domain, [])


def register_action(action: Action, domain: str = "general") -> None:
    """Register a new action."""
    ALL_ACTIONS.append(action)


def get_action_by_name(name: str) -> Action:
    """Get a specific action by name."""
    for action in ALL_ACTIONS:
        if action.name == name:
            return action
    raise ValueError(f"Action '{name}' not found")


# Export everything
__all__ = [
    "ALL_ACTIONS",
    "get_all_actions",
    "get_actions_by_domain", 
    "register_action",
    "get_action_by_name",
    "lead_actions",
    "opportunity_actions",
    "task_actions"
]
