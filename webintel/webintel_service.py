import re
import requests
from bs4 import BeautifulSoup
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, HttpUrl
from typing import List, Optional, Dict, Union
from urllib.parse import urljoin, urlparse

router = APIRouter()

class CompanyInfo(BaseModel):
    company_name: Optional[str] = None
    description: Optional[str] = None
    logo: Optional[HttpUrl] = None
    social_links: Dict[str, HttpUrl] = {}
    internal_links: List[HttpUrl] = []
    external_links: List[HttpUrl] = []
    # New address fields
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    pincode: Optional[str] = None
    street: Optional[str] = None
    contact: Dict[str, Optional[Union[List[str], HttpUrl]]] = {
        "emails": [],
        "phone_numbers": [],
        "contact_page": None
    }

def get_domain(url: str) -> str:
    """Extracts the domain from a URL."""
    parsed_url = urlparse(url)
    return parsed_url.netloc

async def fetch_html(url: str):
    """Fetches HTML content from a URL."""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status() # Raise an exception for bad status codes
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

@router.get("/webintel", response_model=CompanyInfo)
async def fetch_company_info(url: HttpUrl = Query(..., description="The URL of the company's website")):
    """Extracts company information from the given URL."""
    html_content = await fetch_html(str(url))
    if not html_content:
        raise HTTPException(status_code=400, detail=f"Could not fetch content from {url}")

    soup = BeautifulSoup(html_content, 'html.parser')
    info = CompanyInfo()
    domain = get_domain(str(url))

    # Extract Company Name from title
    if soup.title and soup.title.string:
        info.company_name = soup.title.string.strip()

    # Extract Description
    description_tag = soup.find('meta', attrs={'name': 'description'})
    if description_tag and description_tag.get('content'):
        info.description = description_tag.get('content').strip()
    else:
        og_description_tag = soup.find('meta', attrs={'property': 'og:description'})
        if og_description_tag and og_description_tag.get('content'):
            info.description = og_description_tag.get('content').strip()

    # Get Logo
    info.logo = f"https://logo.clearbit.com/{domain}" # type: ignore

    # Extract Social Media Links without query parameters
    social_keywords = ['linkedin', 'twitter', 'facebook', 'instagram', 'x', 'youtube']  # Add more as needed
    for a_tag in soup.find_all('a', href=True):
        raw_href = a_tag['href']
        full_href = urljoin(str(url), raw_href)
        parsed = urlparse(full_href)
        clean_href = parsed._replace(query='', fragment='').geturl()
        for keyword in social_keywords:
            if keyword in clean_href.lower():
                if keyword not in info.social_links or len(clean_href) < len(str(info.social_links[keyword])):
                    info.social_links[keyword] = clean_href  # type: ignore
                    break
    
    # Extract Contact Info (Emails and Phone Numbers)
    text_content = soup.get_text()
    info.contact['emails'] = list(set(re.findall(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', text_content)))
    # Extract and normalize phone numbers by last 10 digits to remove duplicates with country code variations
    raw_phone_numbers = re.findall(r'\+?[0-9\s\-\(\)]*\d{3,}', text_content)
    phone_dict = {}
    for num in raw_phone_numbers:
        digits_only = re.sub(r'\D', '', num)
        if len(digits_only) >= 10:
            core = digits_only[-10:]  # last 10 digits
            normalized = f"+{core}"
            phone_dict[core] = normalized
    info.contact['phone_numbers'] = list(phone_dict.values())

    # Extract Address Components if available via schema.org tags
    addr_country = soup.find(attrs={'itemprop': 'addressCountry'})
    if addr_country and addr_country.get_text():
        info.country = addr_country.get_text().strip()
    addr_state = soup.find(attrs={'itemprop': 'addressRegion'})
    if addr_state and addr_state.get_text():
        info.state = addr_state.get_text().strip()
    addr_city = soup.find(attrs={'itemprop': 'addressLocality'})
    if addr_city and addr_city.get_text():
        info.city = addr_city.get_text().strip()
    addr_pincode = soup.find(attrs={'itemprop': 'postalCode'})
    if addr_pincode and addr_pincode.get_text():
        info.pincode = addr_pincode.get_text().strip()
    addr_street = soup.find(attrs={'itemprop': 'streetAddress'})
    if addr_street and addr_street.get_text():
        info.street = addr_street.get_text().strip()
    
    # Fallback: If no schema.org tags, try <address> or <footer> element for full address text
    if not any([info.country, info.state, info.city, info.pincode, info.street]):
        address_tag = soup.find('address')
        full_addr = None
        if address_tag and address_tag.get_text():
            full_addr = address_tag.get_text().strip()
        else:
            footer_tag = soup.find('footer')
            if footer_tag and footer_tag.get_text():
                footer_text = footer_tag.get_text(separator='\n').strip()
                # Look for line containing a 6-digit pincode
                for line in footer_text.split('\n'):
                    if re.search(r'\b\d{6}\b', line):
                        full_addr = line.strip()
                        break
        if full_addr:
            # Assign full address content to street field initially
            info.street = full_addr
            # Parse full address string into components
            # Look for a 6-digit pincode
            pincode_match = re.search(r'\b\d{6}\b', full_addr)
            if pincode_match:
                info.pincode = pincode_match.group()
            # Remove any "-<pincode>" suffix
            addr_without_pincode = re.sub(r'\s*-\s*\d{6}\b', '', full_addr)
            # Split by commas
            parts = [part.strip() for part in addr_without_pincode.split(',') if part.strip()]
            if len(parts) >= 3:
                # Assume last part is country, second last is state, third last is city
                info.country = parts[-1]
                info.state = parts[-2]
                info.city = parts[-3]
                # Street is everything before city
                info.street = ', '.join(parts[:-3])

    # If we have a valid pincode, fetch detailed address info from the postal API
    if info.pincode:
        try:
            postal_resp = requests.get(f"https://api.postalpincode.in/pincode/{info.pincode}", timeout=10)
            postal_data = postal_resp.json()
            if postal_data and postal_data[0].get("Status") == "Success":
                post_offices = postal_data[0].get("PostOffice", [])
                if post_offices:
                    po = post_offices[0]
                    # Override city, state, and country based on API response
                    info.city = po.get("District", info.city)
                    info.state = po.get("State", info.state)
                    info.country = po.get("Country", info.country)
        except Exception:
            pass

    # Extract internal and external links with occurrence counts
    internal_counts: Dict[str, int] = {}
    external_counts: Dict[str, int] = {}
    for a_tag in soup.find_all('a', href=True):
        raw_href = a_tag['href']
        absolute_url = urljoin(str(url), raw_href)
        parsed_link = urlparse(absolute_url)
        link_domain = parsed_link.netloc
        # Check if internal or external
        if link_domain == domain:
            internal_counts[absolute_url] = internal_counts.get(absolute_url, 0) + 1
        elif parsed_link.scheme in ('http', 'https'):
            external_counts[absolute_url] = external_counts.get(absolute_url, 0) + 1
    # Sort by occurrence and assign to model
    info.internal_links = [
        link for link, _ in sorted(internal_counts.items(), key=lambda item: item[1], reverse=True)
    ]
    info.external_links = [
        link for link, _ in sorted(external_counts.items(), key=lambda item: item[1], reverse=True)
    ]

    # Find Contact Page
    contact_page_found = False
    for a_tag in soup.find_all('a', href=True):
        href = a_tag['href'].lower()
        if 'contact' in href or (a_tag.string and 'contact' in a_tag.string.lower()):
            contact_url = urljoin(str(url), a_tag['href'])
            info.contact['contact_page'] = contact_url # type: ignore
            contact_page_found = True
            # Optionally, scrape the contact page for more details
            # contact_html = await fetch_html(contact_url)
            # if contact_html:
            #     contact_soup = BeautifulSoup(contact_html, 'html.parser')
            #     # Extract more emails/phones from contact page
            #     contact_text = contact_soup.get_text()
            #     info.contact['emails'].extend(list(set(re.findall(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', contact_text))))
            #     info.contact['emails'] = list(set(info.contact['emails'])) # Remove duplicates
            #     info.contact['phone_numbers'].extend(list(set(re.findall(r'\+?[0-9\s\-\(\)]*\d{3,}', contact_text))))
            #     info.contact['phone_numbers'] = list(set(info.contact['phone_numbers'])) # Remove duplicates
            break

    # If no contact page found on homepage, check internal links for a contact page or direct contact info
    if not contact_page_found:
        # Only check internal links containing 'contact'
        contact_links = [l for l in info.internal_links if 'contact' in l.lower()]
        for link in contact_links:
            try:
                link_html = await fetch_html(link)
                if not link_html:
                    continue
                link_soup = BeautifulSoup(link_html, 'html.parser')
                # Attempt to find a contact page link on this internal page
                for a_inner in link_soup.find_all('a', href=True):
                    href_inner = a_inner['href'].lower()
                    if 'contact' in href_inner or (a_inner.string and 'contact' in a_inner.string.lower()):
                        contact_url_inner = urljoin(str(url), a_inner['href'])
                        info.contact['contact_page'] = contact_url_inner  # type: ignore
                        contact_page_found = True
                        break
                if contact_page_found:
                    # Scrape emails and phones from this contact page
                    contact_html_inner = await fetch_html(info.contact['contact_page'])
                    if contact_html_inner:
                        contact_soup_inner = BeautifulSoup(contact_html_inner, 'html.parser')
                        contact_text_inner = contact_soup_inner.get_text()
                        # Extract emails
                        emails_inner = re.findall(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', contact_text_inner)
                        info.contact['emails'].extend(emails_inner)
                        info.contact['emails'] = list(set(info.contact['emails']))
                        # Extract phone numbers
                        raw_nums_inner = re.findall(r'\+?[0-9\s\-\(\)]*\d{3,}', contact_text_inner)
                        for num_inner in raw_nums_inner:
                            digits_only_inner = re.sub(r'\D', '', num_inner)
                            if len(digits_only_inner) >= 10:
                                core_inner = digits_only_inner[-10:]
                                normalized_inner = f"+{core_inner}"
                                if normalized_inner not in info.contact['phone_numbers']:
                                    info.contact['phone_numbers'].append(normalized_inner)
                    break
                else:
                    # If no explicit contact page link, attempt to extract any emails/phones directly from this internal page
                    link_text = link_soup.get_text()
                    emails_inline = re.findall(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', link_text)
                    phones_inline = re.findall(r'\+?[0-9\s\-\(\)]*\d{3,}', link_text)
                    for email_inline in emails_inline:
                        if email_inline not in info.contact['emails']:
                            info.contact['emails'].append(email_inline)
                    for phone_inline in phones_inline:
                        digits_inline = re.sub(r'\D', '', phone_inline)
                        if len(digits_inline) >= 10:
                            core_inline = digits_inline[-10:]
                            normalized_inline = f"+{core_inline}"
                            if normalized_inline not in info.contact['phone_numbers']:
                                info.contact['phone_numbers'].append(normalized_inline)
            except Exception:
                continue

    return info